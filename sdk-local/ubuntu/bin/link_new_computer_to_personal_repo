# This file setup the symbolic links from your computer to the generic scripts in user-template repo, so you'll get the generic scripts
# circles-zone/sdk-local/sdk-local/ubuntu/bin/link_new_computer_to_personal_repo
# You should run this script only once

# cd
# ll | grep '>'

export genericUserHomeDirectoryTemplate='/repos/circlez/user-template/home-directory'
echo genericUserHomeDirectoryTemplate=$genericUserHomeDirectoryTemplate

# Ubuntu
ln -s $genericUserHomeDirectoryTemplate/ubuntu/.bash_aliases ~/.bash_aliases
ln -s $genericUserHomeDirectoryTemplate/ubuntu/.bashrc ~/.bashrc
ln -s $genericUserHomeDirectoryTemplate/ubuntu/.bash_profile ~/.bash_profile
ln -s $genericUserHomeDirectoryTemplate/ubuntu/.profile ~/.profile

# Powershell
# Backup (TODO not working)
if [ -f ~/.config/powershell/Microsoft.PowerShell_profile.ps1 ]; then
    echo "Backup ~/.config/powershell/Microsoft.PowerShell_profile.ps1"
    mv ~/.config/powershell/Microsoft.PowerShell_profile.ps1 ~tal/.config/powershell/Microsoft.PowerShell_profile.ps1.original
fi

echo "ls $genericUserHomeDirectoryTemplate/.config/powershell/Microsoft.PowerShell_profile.ps1"
ls -lag $genericUserHomeDirectoryTemplate/.config/powershell/Microsoft.PowerShell_profile.ps1

# I didn't manage to use symboic file here in PowerShell, we we copy it
# if [ -f $genericUserHomeDirectoryTemplate/.config/powershell/Microsoft.PowerShell_profile.ps1 ]; then
    # echo "Linking ~/.config/powershell/Microsoft.PowerShell_profile.ps1 to $genericUserHomeDirectoryTemplate/powershell/Microsoft.# PowerShell_profile.ps1"
    # ln -s $genericUserHomeDirectoryTemplate/powershell/Microsoft.PowerShell_profile.ps1 ~/.config/powershell/Microsoft.PowerShell_profile.ps1
# fi

cp $genericUserHomeDirectoryTemplate/.config/powershell/Microsoft.PowerShell_profile.ps1 ~/.config/powershell/Microsoft.PowerShell_profile.ps1

echo "ls"
ls -lag ~/.config/powershell/Microsoft.PowerShell_profile.ps1
