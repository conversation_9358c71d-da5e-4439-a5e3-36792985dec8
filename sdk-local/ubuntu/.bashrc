# To run this file from bash I used `source ~/repos/circlez/sdk-local/sdk-local/ubuntu/bin/.bashrc`

echo circlez.ai circles-zone/sdk-local/sdk-local/ubuntu/.bashrc

# TODO Use the directory the user setup in her/his ~/.bashrc
if [ -f ~/repos/circlez/sdk-local/sdk-local/ubuntu/bin/.bash_aliases ]; then
    . ~/repos/circlez/sdk-local/sdk-local/ubuntu/bin/.bash_aliases
fi


# TODO Do we need this?
alias pyenvi="~/.pyenv/bin/pyenv"

echo "sdk-local/sdk-local/ubuntu/.bashrc Run pyenv from ~/.pyenv/bin/pyenv"

export PYENV_ROOT="$HOME/.pyenv"
echo "sdk-local/ubuntu/.bashrc PYENV_ROOT=$PYENV_ROOT"

# export PATH="$PYENV_ROOT/bin:$PATH"
export PATH="$HOME/.pyenv/bin:$PATH"
# To resolve the error: pyenv: shell integration not enabled. Run `pyenv init' for instructions.
echo "sdk-local/sdk-local/ubuntu/.bashrc penv init -"
eval "$(pyenv init -)"
eval "$(pyenv init --path)"
eval "$(pyenv init -)"
eval "$(pyenv virtualenv-init -)"