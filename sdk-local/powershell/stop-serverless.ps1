# Loop through all "node" processes
Get-Process node -ErrorAction SilentlyContinue | ForEach-Object {
    try {
        # Get the full command line using .NET
        $commandLine = (Get-CimInstance Win32_Process -Filter "ProcessId = $($_.Id)").CommandLine

        # If command line contains both "serverless" and "offline", kill it
        if ($commandLine -match "serverless" -and $commandLine -match "offline") {
            Write-Host "Killing Serverless Offline process (PID $($_.Id))"
            Stop-Process -Id $_.Id -Force
        }
    } catch {
        # Swallow errors for processes that disappear mid-iteration or can't be accessed
    }
}