# Analyze the current directory
$currentDirectory = (Split-Path $PWD.Path -Leaf)
Write-Host "Current directory=" $currentDirectory

if ($currentDirectory.contains("python")) {
    $execute = "~/repos/circlez/python-sdk-remote-python-package/python-sdk-remote-python-package/powershell/make_py.ps1"
    # TODO Create a function from those three lines
    if ($null -eq (Get-Command $execute -ErrorAction SilentlyContinue)) {
        git clone https://github.com/circles-zone/python-sdk-remote-python-package.git ~/repos/circlez
    }
    # TODO Needed only one time
    chmod +x $execute
    & $execute @args
# } elseif ($currentDirectory -contains "typescript") {
} elseif ($currentDirectory.contains("typescript")) {
    # Write-Host "make_ts " @args
    make_ts @args
} else {
    Write-Host "makec: Unknown language in directory $currentDirectory name"
}
