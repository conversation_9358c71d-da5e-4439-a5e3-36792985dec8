# Convert delimiters to dots (for semver format)
# TODO Shall we rename this function to ConvertToDots?
function Convert-ToSemver {
    param (
        [Parameter(Mandatory=$true)]
        [string]$InputString
    )
    
    # Replace any non-alphanumeric characters (except dots) with dots
    $cleanedString = $InputString -replace '[^a-zA-Z0-9.]', '.'
    
    # Remove consecutive dots
    $cleanedString = $cleanedString -replace '\.+', '.'
    
    # Remove leading/trailing dots
    $cleanedString = $cleanedString.Trim('.')
    
    return $cleanedString
}
