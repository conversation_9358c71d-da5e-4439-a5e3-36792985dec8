# /repos/circlez/tal-circlez.ai/powershell/find_duplicate_files.ps1 .

param(
    [Parameter(Mandatory=$true)]
    [string]$directory,
    [string]$jiraUrl = $env:JIRA_URL,
    [string]$jiraToken = $env:JIRA_TOKEN,
    [string]$jiraProject = $env:JIRA_PROJECT
)

$isPlan = $true

# TODO change to $true and fix access to Jira
$isJira = $false

$numberOfFileGroups = 5

if ( $directory -eq "\repos\circlez") {
    $isMultiRepos = $true
}
else {
    $isMultiRepos = $false
}

function Find-DuplicateFiles {
    param([string]$path)
    
    # TODO Shorter but not working, can we use -notcontains
    # $files = Get-ChildItem -Path $path -Recurse -File | Where-Object { $_.FullName -notcontains {@("*node_modules*", "*dist*", "*__init__.py", "*__.d.ts", "*__.js", "*_addMapEntry.js") } }
    # Working but longer
    $files = Get-ChildItem -Path $path -Recurse -File | Where-Object { $_.FullName -notlike "*node_modules*" -and $_.FullName -notlike "*dist*" -and $_.FullName -notlike "*__init__.py" -and $_.FullName -notlike "*__.d.ts" -and $_.FullName -notlike "*__.js" -and $_.FullName -notlike "*_addMapEntry.js" -and $_.FullName -notlike "*angular*" -and $_.FullName -notlike "*action.yml" }

    # $duplicates = $files | Group-Object Name | Where-Object { $_.Count -gt 1 }
    $duplicates = $files | Group-Object Name | Where-Object { $_.Count -gt 1 } | Select-Object -First $numberOfFileGroups

    if ($duplicates) {
        Write-Host "Duplicate files found in $path. TODO Please fix each one in a separate Task/Branch" -ForegroundColor Red
        foreach ($group in $duplicates) {
            Write-Host "`nFilename: $($group.Name) (Found $($group.Count) times)" -ForegroundColor Yellow
            $group.Group | ForEach-Object { Write-Host "  - $($_.FullName)" }
        }
        return $duplicates
    } else {
        Write-Host "No duplicate files found in $path" -ForegroundColor Green
        return $null
    }
}

function Test-JiraIssueExists {
    param([string]$directory)
    
    $jql = "project = '$jiraProject' AND summary ~ 'Duplicate files in $directory'"
    $searchUrl = "$jiraUrl/rest/api/2/search?jql=$([System.Web.HttpUtility]::UrlEncode($jql))"
    
    try {
        $headers = @{ Authorization = "Bearer $jiraToken"; "Content-Type" = "application/json" }
        Write-Host $searchUrl
        Write-Host $headers
        # $response = Invoke-RestMethod -Uri $searchUrl -Headers $headers -Method Get
        # https://developer.atlassian.com/cloud/jira/platform/rest/v2/api-group-issue-search/#api-rest-api-2-search-get
        Write-Host "AAA"
        $response = Invoke-RestMethod -Uri "https://circles-zone.atlassian.net/rest/api/2/search?jql=project%20%3D%20BU" -Method GET
            # -Username '<EMAIL>:$jiraToken' \
            # -Headers 'Accept: application/json'
            # -Authentication Basic
            # -Uri $searchUrl \
        Write-Host "BBB"

        Write-Host $response
        return $response.total -gt 0
    } catch {
        Write-Warning "Failed to check existing Jira issues: $($_.Exception.Message)"
        return $false
    }
}

function New-JiraIssue {
    param([string]$directory, [array]$duplicates)
    
    $description = "Duplicate files detected in directory: $directory`n`n"
    foreach ($group in $duplicates) {
        $description += "Filename: $($group.Name) (Found $($group.Count) times)`n"
        $group.Group | ForEach-Object { $description += "- $($_.FullName)`n" }
        $description += "`n"
    }
    
    $issueData = @{
        fields = @{
            project = @{ key = $jiraProject }
            summary = "Duplicate files found in $directory"
            description = $description
            issuetype = @{ name = "Bug" }
        }
    } | ConvertTo-Json -Depth 3
    
    try {
        if ( -not $isPlan ) {
            $headers = @{ Authorization = "Bearer $jiraToken"; "Content-Type" = "application/json" }
            $response = Invoke-RestMethod -Uri "$jiraUrl/rest/api/2/issue" -Headers $headers -Method Post -Body $issueData
            Write-Host "Jira issue created: $jiraUrl/browse/$($response.key)" -ForegroundColor Green
        }
    } catch {
        Write-Error "Failed to create Jira issue: $($_.Exception.Message)"
    }
}

if ($isJira) {
    /repos/circlez/tal-circlez.ai/powershell/set_jira.ps1
}

# Main execution
if (-not (Test-Path $directory)) {
    Write-Error "Directory '$directory' does not exist"
    exit 1
}

$duplicates = Find-DuplicateFiles -path $directory

if ($duplicates -and $isJira -and $jiraUrl -and $jiraToken -and $jiraProject) {
    if (-not (Test-JiraIssueExists -directory $directory)) {
        New-JiraIssue -directory $directory -duplicates $duplicates
    } else {
        Write-Host "Jira issue already exists for this directory" -ForegroundColor Yellow
    }
} elseif ($duplicates -and $isJira) {
    Write-Host "Jira configuration missing. Set JIRA_URL, JIRA_TOKEN, and JIRA_PROJECT environment variables to create issues." -ForegroundColor Yellow
}