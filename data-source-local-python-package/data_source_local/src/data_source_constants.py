from logger_local.LoggerComponentEnum import LoggerComponentEnum

DATA_SOURCE_LOCAL_COMPONENT_ID = 117
DATA_SOURCE_LOCAL_COMPONMENT_NAME = 'data-source-local-python-package'

data_source_logger_code_obj = {
    'component_id': DATA_SOURCE_LOCAL_COMPONENT_ID,
    'component_name': DATA_SOURCE_LOCAL_COMPONMENT_NAME,
    'component_category': LoggerComponentEnum.ComponentCategory.Code.value,
    'developer_email': '<EMAIL>'
}
