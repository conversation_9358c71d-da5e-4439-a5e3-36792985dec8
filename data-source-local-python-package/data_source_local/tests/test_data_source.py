from datetime import datetime

from src.data_source import DataSources

SOURCE_NAME = "TEST" + str(datetime.now())
SOURCE_NAME_INVALID = "INVALID"
# TODO data_source_types = DataSourceTypes()
data_sources = DataSources()


def test_insert_select():
    ds = DataSources()
    insert_data_source_type_id = ds.insert_data_source_type(SOURCE_NAME)
    select_data_source_type_id = ds.get_data_source_type_id_by_name(SOURCE_NAME)
    assert insert_data_source_type_id != select_data_source_type_id


def test_select_invalid():
    ds = DataSources()
    select_data_source_type_id = ds.get_data_source_type_id_by_name(SOURCE_NAME_INVALID)
    assert select_data_source_type_id is None
