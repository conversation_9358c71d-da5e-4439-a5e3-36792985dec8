# https://python-poetry.org/docs/pyproject

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.poetry]
name = "data-source-local"
# I believe we are still using the version from setup.py and not from here until potery will work
version = "0.0.15" # https://pypi.org/project/data-source-local
description = "Data Source Python Package"
readme = "README.md"
authors = [
    "Circlez.ai <<EMAIL>>",
]
