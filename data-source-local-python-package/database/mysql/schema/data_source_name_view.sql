CREATE ALGORITHM = UNDEFINED DEFINER =`tal`@`%` SQL SECURITY DEFINER VIEW `data_source`.`data_source_ml_en_view` AS
select data_source_table.id, data_source_ml_table.data_source_name
from (`data_source`.`data_source_ml_table` join `data_source`.`data_source_table`)
where ((`data_source`.`data_source_ml_table`.`data_source_id` = `data_source`.`data_source_table`.`id`) and
       (`data_source`.`data_source_ml_table`.`start_timestamp` <= curtime()) and
       ((`data_source`.`data_source_ml_table`.`end_timestamp` is null) or
        (`data_source`.`data_source_ml_table`.`end_timestamp` >= curtime())) and
       (`data_source`.`data_source_ml_table`.`data_source_name_approved` = true) and
       (`data_source`.`data_source_ml_table`.`lang_code` = 'en'));
