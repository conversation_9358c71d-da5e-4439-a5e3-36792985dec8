CREATE TABLE `data_source_type_table` (
  `data_source_type_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK: source_id',
  `name` varchar(45) NOT NULL,
  `system_id` smallint unsigned DEFAULT NULL,
  `subsystem_id` smallint unsigned DEFAULT NULL,
  `data_source_category_id` int unsigned DEFAULT NULL,
  `is_test_data` tinyint DEFAULT NULL,
  `visibility_id` bigint unsigned DEFAULT '1',
  `start_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_timestamp` timestamp NULL DEFAULT NULL,
  `created_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_user_id` bigint unsigned NOT NULL,
  `updated_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_user_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`data_source_type_id`),
  UNIQUE KEY `id` (`data_source_type_id`),
  <PERSON><PERSON><PERSON> `created_user_id` (`created_user_id`),
  <PERSON><PERSON><PERSON> `updated_user_id` (`updated_user_id`),
  KEY `system_id_fk_idx` (`system_id`),
  KEY `data_source.data_source_type.id.fk_idx` (`data_source_category_id`),
  KEY `data_source.subsystem_id.fk_idx` (`subsystem_id`),
  CONSTRAINT `data_source.subsystem_id.fk` FOREIGN KEY (`subsystem_id`) REFERENCES `system`.`subsystem_table` (`subsystem_id`),
  CONSTRAINT `data_source_type_table_ibfk_1` FOREIGN KEY (`created_user_id`) REFERENCES `user`.`user_table` (`user_id`),
  CONSTRAINT `data_source_type_table_ibfk_2` FOREIGN KEY (`updated_user_id`) REFERENCES `user`.`user_table` (`user_id`),
  CONSTRAINT `system_id_fk` FOREIGN KEY (`system_id`) REFERENCES `system`.`system_table` (`system_id`)
) ENGINE=InnoDB AUTO_INCREMENT=65 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
