CREATE TABLE `data_source_category_ml_table` (
  `data_source_category_ml_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK: source_ml_id',
  `data_source_category_id` int unsigned NOT NULL,
  `lang_code` char(5) NOT NULL,
  `data_source_name` varchar(255) NOT NULL,
  `data_source_name_approved` tinyint(1) DEFAULT '0',
  `start_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_timestamp` timestamp NULL DEFAULT NULL,
  `created_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_user_id` bigint unsigned NOT NULL,
  `updated_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_user_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`data_source_category_ml_id`),
  UNIQUE KEY `id` (`data_source_category_ml_id`),
  <PERSON><PERSON><PERSON> `created_user_id` (`created_user_id`),
  <PERSON><PERSON>Y `updated_user_id` (`updated_user_id`),
  <PERSON><PERSON><PERSON> `data_source_ml.lang_code.fk_idx` (`lang_code`),
  KEY `data_source_type_ml.data_source_id.fk_idx` (`data_source_category_id`),
  CONSTRAINT `data_source_type_ml.created_user_id.fk` FOREIGN KEY (`created_user_id`) REFERENCES `user`.`user_table` (`user_id`),
  CONSTRAINT `data_source_type_ml.data_source_id.fk` FOREIGN KEY (`data_source_category_id`) REFERENCES `data_source_category_table` (`data_source_category_id`),
  CONSTRAINT `data_source_type_ml.lang_code.fk` FOREIGN KEY (`lang_code`) REFERENCES `lang_code`.`lang_code_table` (`lang_code`),
  CONSTRAINT `data_source_type_ml.updated_user_id.fk` FOREIGN KEY (`updated_user_id`) REFERENCES `user`.`user_table` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
