CREATE TABLE `data_source_type__field_table` (
  `data_source_type__field_id` int unsigned NOT NULL AUTO_INCREMENT,
  `data_source_type_id` int unsigned DEFAULT NULL,
  `field_id` smallint unsigned DEFAULT NULL,
  `external_field_name` varchar(45) DEFAULT NULL,
  `position` smallint unsigned DEFAULT NULL,
  PRIMARY KEY (`data_source_type__field_id`),
  UNIQUE KEY `data_source_type__field_id_UNIQUE` (`data_source_type__field_id`),
  <PERSON>EY `data_source_id_idx` (`data_source_type_id`),
  KEY `field_id_idx` (`field_id`),
  CONSTRAINT `data_source_type_id` FOREIGN KEY (`data_source_type_id`) REFERENCES `data_source`.`data_source_type_table` (`data_source_type_id`),
  CONSTRAINT `field_id` FOREIGN KEY (`field_id`) REFERENCES `field`.`field_table` (`field_id`)
) ENGINE=InnoDB AUTO_INCREMENT=74 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
