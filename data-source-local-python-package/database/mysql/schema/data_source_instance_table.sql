CREATE TABLE `data_source_instance_table` (
  `data_source_instance_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `number` bigint unsigned DEFAULT NULL,
  `identifier` varchar(255) DEFAULT NULL,
  `name` varchar(65) NOT NULL,
  `data_source_type_id` int unsigned NOT NULL,
  `file_or_api` varchar(45) NOT NULL,
  `computer_name` varchar(45) DEFAULT NULL,
  `path` varchar(65) DEFAULT NULL,
  `filename` varchar(45) DEFAULT NULL,
  `imported_file_created_timestamp` timestamp NULL DEFAULT NULL COMMENT 'Not the time we created the record.',
  `import_or_last_sync_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `user_external_id` bigint unsigned NOT NULL,
  `start_index` int unsigned DEFAULT NULL,
  `end_index` int unsigned DEFAULT NULL,
  `visibility_id` bigint NOT NULL DEFAULT '1',
  `is_test_data` tinyint DEFAULT NULL,
  `start_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_timestamp` timestamp NULL DEFAULT NULL,
  `created_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_user_id` bigint unsigned NOT NULL COMMENT 'TODO We need to remove this',
  `created_real_user_id` bigint unsigned NOT NULL,
  `created_effective_user_id` bigint unsigned NOT NULL,
  `created_effective_profile_id` bigint unsigned NOT NULL,
  `updated_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_user_id` bigint unsigned NOT NULL COMMENT 'TODO We need to remove this',
  `updated_real_user_id` bigint unsigned NOT NULL,
  `updated_effective_user_id` bigint unsigned NOT NULL,
  `updated_effective_profile_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`data_source_instance_id`),
  UNIQUE KEY `data_source_instance_id_UNIQUE` (`data_source_instance_id`),
  UNIQUE KEY `name_UNIQUE` (`name`),
  KEY `data_source_instance.data_source_type_id_idx` (`data_source_type_id`),
  KEY `data_source_instance.user_external_id.fk_idx` (`user_external_id`),
  CONSTRAINT `data_source_instance.data_source_type_id` FOREIGN KEY (`data_source_type_id`) REFERENCES `data_source`.`data_source_type_table` (`data_source_type_id`),
  CONSTRAINT `data_source_instance.user_external_id.fk` FOREIGN KEY (`user_external_id`) REFERENCES `user_external`.`user_external_table` (`user_external_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3461 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
