CREATE TABLE `data_source_external_user_table` (
  `data_source_external_user_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK: source_id',
  `data_source_id` int unsigned DEFAULT NULL,
  `user_external_id` bigint unsigned DEFAULT NULL,
  `is_test_data` tinyint DEFAULT NULL,
  `start_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_timestamp` timestamp NULL DEFAULT NULL,
  `created_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_user_id` bigint unsigned NOT NULL,
  `updated_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_user_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`data_source_external_user_id`),
  UNIQUE KEY `data_source_external_user_id` (`data_source_external_user_id`),
  <PERSON><PERSON><PERSON> `created_user_id` (`created_user_id`),
  <PERSON><PERSON><PERSON> `updated_user_id` (`updated_user_id`),
  CONSTRAINT `data_source_external_user_table_ibfk_1` FOREIGN KEY (`created_user_id`) REFERENCES `user`.`user_table` (`user_id`),
  CONSTRAINT `data_source_external_user_table_ibfk_2` FOREIGN KEY (`updated_user_id`) REFERENCES `user`.`user_table` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
