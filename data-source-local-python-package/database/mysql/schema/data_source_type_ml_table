CREATE TABLE `data_source_type_ml_table` (
  `data_source_type_ml_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK: source_ml_id',
  `data_source_type_id` int unsigned NOT NULL,
  `lang_code` char(5) NOT NULL,
  `title` varchar(255) NOT NULL,
  `is_test_data` tinyint DEFAULT NULL,
  `start_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_timestamp` timestamp NULL DEFAULT NULL,
  `created_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_user_id` bigint unsigned NOT NULL,
  `updated_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_user_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`data_source_type_ml_id`),
  UNIQUE KEY `id` (`data_source_type_ml_id`),
  UNIQUE KEY `idx_source_id_lang_code` (`data_source_type_id`,`lang_code`),
  <PERSON><PERSON><PERSON> `created_user_id` (`created_user_id`),
  <PERSON>EY `updated_user_id` (`updated_user_id`),
  KEY `data_source_ml.lang_code.fk_idx` (`lang_code`),
  CONSTRAINT `data_source_ml.lang_code.fk` FOREIGN KEY (`lang_code`) REFERENCES `lang_code`.`lang_code_table` (`lang_code`),
  CONSTRAINT `data_source_type_ml_table_ibfk_1` FOREIGN KEY (`data_source_type_id`) REFERENCES `data_source_type_table` (`data_source_type_id`),
  CONSTRAINT `data_source_type_ml_table_ibfk_2` FOREIGN KEY (`created_user_id`) REFERENCES `user`.`user_table` (`user_id`),
  CONSTRAINT `data_source_type_ml_table_ibfk_3` FOREIGN KEY (`updated_user_id`) REFERENCES `user`.`user_table` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=57 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
