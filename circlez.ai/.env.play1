# TODO The bellow lines do not work in TypeScript, does it work in Python
#DOTENV_PATH="d:\repos\circles\.env.prod1"
#DOTENV_PATH="/repos/circlez/tal-circlez.ai/.env.play1"

VITE_APP_TITLE=Circlez.ai (play1)

BRAND_NAME=Circlez
VITE_BRAND_NAME=Circlez

ENVIRONMENT_NAME=play1
VITE_ENVIRONMENT_NAME=play1

VITE_REACTJS_ENVIRONMENT=

# TODO
VITE_BACKEND_URL:

# Play1
# LOGZIO_TOKEN is not working
# LOGZIO_TOKEN="SsCVwKuHRlNjUJrfGgjIUIcHKpRODxla"

# RDS_HOSTNAME="mysql-db.dvlp1.circ.zone"
# Should not have quotes " around the hostname
RDS_HOSTNAME=mysql-db.play1.circ.zone
# RDS_USERNAME="akiva.s"
# RDS_PASSWORD="Aki1Va!Circ2"
RDS_USERNAME=tal
RDS_PASSWORD=TaL1Db!TaL$#

REDIS_HOSTNAME="redis-15230.c256.us-east-1-2.ec2.redns.redis-cloud.com"
# REDIS_HOSTNAME="redis.dvlp1.circ.zone"
REDIS_PORT=15230 # The default port for Redis is 6379
REDIS_PASSWORD="9QypkYSFjCQLNPeMP0H1Op2FJ69C2oav"
#REDIS_DATABASE="Tal-free-db" # TODO What do we do with this value?

# postgresql-db.play1.circ.zone
POSTGRESQL_HOSTNAME="play1-us-east-1a.ccl3gjevsfnb.us-east-1.rds.amazonaws.com"
# POSTGRESQL_POST=5432
POSTGRESQL_USERNAME="play1"
POSTGRESQL_PASSWORD="Tal1Play1!"

# Serverside
USER_JWT_SECRET_KEY=4rehrkj43h5lkefkdslcw4r

#
# PRODUCT_USER_IDENTIFIER ="<EMAIL>"
# PRODUCT_PASSWORD="<EMAIL>"

# user_id=5000041
# profile_id=5000062
PRODUCT_USER_IDENTIFIER=<EMAIL>
VITE_PRODUCT_USER_IDENTIFIER=<EMAIL>
PRODUCT_PASSWORD=idos_very_secret_passwordX
VITE_PRODUCT_PASSWORD=idos_very_secret_passwordX

LOGGER_MINIMUM_SEVERITY="Info"
# LOGGER_CONFIGURATION_JSON_PATH=C:\Users\<USER>\PycharmProjects\circles\.logger.json
LOGGER_CONFIGURATION_JSON_PATH=/repos/circlez/tal-circlez.ai/circlez.ai/.logger.json
LOGGER_IS_WRITE_TO_SQL=False

AWS_DEFAULT_STORAGE_BUCKET_NAME="storage.us-east-1.play1.circ.zone"

AWS_DEFAULT_REGION=us-east-1
#REGION=us-east-1

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=GSYwOWCo3kim64y65Cb+GlE24oW2kYb+WEkAjd5O

ZOOMINFO_APPLICATION_CLIENT_ID=f72ejPnQVKAb5zH6QeLg
ZOOMINFO_APPLICATION_CLIENT_SECRET=e7Hf4j3z9P2HFPw3ImLxgdeW0r2CgS48
ZOOMINFO_APPLICATION_ACCOUNT_ID=q3eRv6iRQ86FzozIj7sZWA

DEFAULT_SENDER_PROFILE_ID=0
TEST_DESTINATION_PHONE_NUMBER="**********"
TEST_SENDER_PROFILE_ID=1

INFORU_AUTH_TOKEN="Basic ZW1hZC5hOjNiOGI3NjkyLWJmNjItNDg2MC1hOTU0LWVhYWNhOWM4ZmFjZg=="

# OPENCAGE_KEY=c9e46a40d9a64abb945a436260ba84da
# opencagedata.com <EMAIL> Geocoding API
OPENCAGE_KEY=********************************
#TODO We should switch from OPENCAGE_KEY to OPENCAGE_API_KEY
OPENCAGE_API_KEY=********************************

# TODO Why those starts with TEST_
TEST_ZOOMINFO_USER_ID=Hb_kQvq4RHWEi8BdGwAxOg
TEST_ZOOMINFO_FIRST_NAME=Akiva
TEST_ZOOMINFO_LAST_NAME=Skolnik
TEST_ZOOMINFO_EMAIL=<EMAIL>

REDDIT_USERNAME=test_circles
REDDIT_CLIENT_SECRET=3g0IkF72NSkFQkLRaBev8TuGFwiAEg
REDDIT_CLIENT_ID=pdK3e3rQVXevMYqxv38-CQ

YELP_API_KEY="********************************************************************************************************************************"

DISABLE_HEADLESS_MODE=TRUE
PYTHONIOENCODING=utf-8

# Google Account / Google Authentication
# TODO Can't we delete the bellow line?
# PORT_FOR_AUTHENTICATION=54219

# Google Accounts
# GOOGLE_PORT_FOR_AUTHENTICATION=54219
GOOGLE_REDIRECT_URIS=http://localhost:54219/
GOOGLE_TOKEN_URI=https://oauth2.googleapis.com/token
GOOGLE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
GOOGLE_REDIRECT_URIS=https://viyjn92gcj.execute-api.us-east-1.amazonaws.com/play1/api/v1/googleAccount/getCode

GOOGLE_USER_EXTERNAL_USERNAME=<EMAIL>
# GOOGLE_USER_EXTERNAL_USERNAME=<EMAIL>

# For PeopleAPI - examples should be replaced with Circlezproject later on, the rest are static
# TODO Shall we also support CONTACTS_GOOGLE_CLIENT_ID ... so we can support different GCP project for google-contact and google-sheets
# GOOGLE_PROJECT_ID=projectapi-396517
GOOGLE_CLIENT_ID=************-t9hriu2ooqutdgj5ku74gv4fursbv0gm.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# TODO Make sure it works also with _CONTACTS_ works
# GOOGLE_CONTACTS_CLIENT_ID=************-t9hriu2ooqutdgj5ku74gv4fursbv0gm.apps.googleusercontent.com
# GOOGLE_CONTACTS_CLIENT_SECRET=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

# Google Contacts (People API)
GOOGLE_CONTACTS_SEQ_START=0
GOOGLE_CONTACTS_SEQ_END=0

# Google Sheets Project OAuth 2.0
# PROJECT_ID can't start/contain with google-
# GOOGLE_SHEETS_PROJECT_ID=gogle-sheets-api-play1
GOOGLE_SHEETS_CLIENT_ID=************-5kckgia6idamgpe4dpbiokvad5105okb.apps.googleusercontent.com
GOOGLE_SHEETS_CLIENT_SECRET=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

# TODO We should support both with and without _SHEETS_
# GOOGLE_CLIENT_ID=************-5kckgia6idamgpe4dpbiokvad5105okb.apps.googleusercontent.com
# GOOGLE_CLIENT_SECRET=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


# Play1
LOCATION_GRAPHQL_APP_SYNC_API_KEY=da2-lv53xyqls5d6bkvq7zuh3ibcra
MESSAGE_GRAPHQL_APPSYNC_API_KEY=da2-5fnct4c5x5bgze3xvrr3n3lyne
TEST_GRAPHQL_APPSYNC_API_KEY=da2-ypjhpdtyo5guzknwpnksbrzw6y

VITE_DEBUG=1
DEBUG=1
DEBUG=True

OPENSEARCH_HOST=opensearch.play1.circ.zone
OPENSEARCH_INITIAL_ADMIN_PASSWORD=Sala1Me$.654321987

# AWS SES approved Identity in the AWS Environment
FROM_EMAIL=<EMAIL>
MESSAGE_AWS_SES_DEFAULT_CONFIGURATION_SET=

# We need those to run "serverless offline start"
# TODO Try to remove this and use ENVIRONMENT_NAME
STAGE=play1
SERVERLESS_LICENSE_KEY=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************.KAA9K1uHXXc7qcjtyofn2BiZCd8cE0CT2eNxb24XOYvyua5hH9gYyc8JBC-AX5o-VrlFDuVZBoMUbcpq25KzE1W8EkBalJLnLoqvlbZ_RqIQOIfGunUK4dmk44wNkzByWS0RkOht2qHNoRrq066qicXNGm9-bZDVtjUBBc9l7RJ-wBmkvOTb6_yRLX3yJZQv27U9UUT1fKXzJ2wXcr-xDthI3Wh-EfxyRBmxGMOCgY_nrk1DmYuuhUyJ_PJFoT2crMx6rFtGFD2kwuLsZ1Zedl4XLwskw_ZpvovSCXN1-kmSShtgT1tzc7Sz2ftyHwjas91155DBuVKXesIEnPD1RQ
