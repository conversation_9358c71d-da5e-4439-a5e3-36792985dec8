{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Launch Serverless Offline",
      "program": "${workspaceFolder}/node_modules/serverless/bin/serverless",
      // TODO: Change the directory to the directory of your serverless
      "args": [
        "offline",
        "start",
        "--httpPort",
        "3000",
        "--noTimeout"
      ],
      "envFile": "${workspaceFolder}/.env",
      "cwd": "${workspaceFolder}",
      // TODO: Specify the working directory
      "runtimeExecutable": "node",
      "windows": {
        "program": "${workspaceFolder}/node_modules/serverless/bin/serverless"
        // TODO: Change the directory to the directory of your serverless
      },
      "console": "integratedTerminal"
    }
  ]
}
