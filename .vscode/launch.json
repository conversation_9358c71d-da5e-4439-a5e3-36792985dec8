{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: Current File",
      "type": "debugpy",
      "request": "launch",
      "program": "${file}",
      "console": "internalConsole",
      "justMyCode": true,
      "cwd": "${workspaceFolder}/logger-local-python-package/logger_local",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/logger-local-python-package/logger_local"
      }
    },
    {
      "name": "Python: test_writer.py",
      "type": "debugpy",
      "request": "launch",
      "program": "python.exe ..//tests//test_writer.py",
      "console": "internalConsole",
      "justMyCode": true
    }
  ]
}