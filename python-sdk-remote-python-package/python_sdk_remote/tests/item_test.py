# from item import Item


# TODO Why do we need to inherit from Item?
# class OurItems(Item):

#     def __init__(self):
#         super().__init__()

#     def get_id(self):
#         return self.id

#     def get_name(self):
#         return "OurItems"

#     def test_get_id(self):
#         return self.id


# def test_our_items():
#     our_items = OurItems()
#     our_items.set_id_value(123)
#     assert our_items.test_get_id() == 123
