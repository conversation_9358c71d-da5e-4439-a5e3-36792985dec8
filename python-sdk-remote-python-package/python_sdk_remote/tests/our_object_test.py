from src.mini_logger import <PERSON><PERSON>ogger as logger
from src.our_object import OurObject
from datetime import datetime

ENTITY_NAME = "OurObjectSubclass"


class OurObjectSubclass(OurObject):

    def __init__(self, entity_name=ENTITY_NAME, **kwargs):
        super().__init__(entity_name, **kwargs)

    def get_id(self):
        get_id_result = 123
        return get_id_result  # Implementing the abstract method get_id

    def get_name(self):
        return "TestObject"  # Implementing the abstract method get_name


def test_our_object():
    TEST_OUR_OBJECT_FUNCTION_NAME = "test_our_object"
    logger.start(TEST_OUR_OBJECT_FUNCTION_NAME)

    our_object_1 = OurObjectSubclass(ENTITY_NAME, a=1, b="Our Object Test")
    our_object_2 = OurObjectSubclass(ENTITY_NAME, a=1, b="Our Object Test")
    our_object_3 = OurObjectSubclass(ENTITY_NAME, id=3, a="Object3", b=3)

    # Test == and != operators
    assert our_object_1 == our_object_2
    assert our_object_1 != our_object_3

    our_object_1.set_id(1)
    our_object_2.set_id(2)

    # Test get() method
    a1 = our_object_1.get("a")
    a2 = our_object_2.get("a")
    a3 = our_object_3.get("a")
    b1 = our_object_1.get("b")
    b2 = our_object_2.get("b")
    b3 = our_object_3.get("b")

    assert a1 == 1
    assert a2 == 1
    assert a3 == "Object3"
    assert b1 == b2 == "Our Object Test"
    assert b3 == 3

    # Test get_all_arguments() method
    assert our_object_1.get_all_arguments() == {'a': 1, 'b': 'Our Object Test'}
    assert our_object_2.get_all_arguments() == {'a': 1, 'b': 'Our Object Test'}
    assert our_object_3.get_all_arguments() == {'a': 'Object3', 'b': 3}

    # Test to_json() and from_json() methods
    our_object_1_json = our_object_1.to_json()
    our_object_2_json = our_object_2.to_json()
    our_object_3_json = our_object_3.to_json()

    assert (
        our_object_1_json
        == '{"entity_name": "OurObjectSubclass", "id": 1, "kwargs": {"a": 1, "b": "Our Object Test"}}'
    )
    assert (
        our_object_2_json
        == '{"entity_name": "OurObjectSubclass", "id": 2, "kwargs": {"a": 1, "b": "Our Object Test"}}'
    )
    assert (
        our_object_3_json
        == '{"entity_name": "OurObjectSubclass", "id": 3, "kwargs": {"a": "Object3", "b": 3}}'
    )

    our_object4 = OurObjectSubclass(id_column_name="our_object_subclass")
    our_object5 = OurObjectSubclass(id_column_name="our_object_subclass")
    our_object6 = OurObjectSubclass(id_column_name="our_object_subclass")
    assert our_object4.from_json(our_object_1_json) == our_object_1
    assert our_object5.from_json(our_object_2_json) == our_object_2
    assert our_object6.from_json(our_object_3_json) == our_object_3

    logger.end(TEST_OUR_OBJECT_FUNCTION_NAME)


def test_abstract_methods():
    test_object = OurObjectSubclass(ENTITY_NAME, a=1, b="Testing Abstract Methods")

    # Test get_id method
    assert test_object.get_id() == 123

    # Test get_name method
    assert test_object.get_name() == "TestObject"


def test_to_json_with_datetime():
    TEST_TO_JSON_WITH_DATETIME_FUNCTION_NAME = "test_to_json_with_datetime"
    logger.start(TEST_TO_JSON_WITH_DATETIME_FUNCTION_NAME)

    test_object = OurObjectSubclass(
        entity_name=ENTITY_NAME,
        id=1,
        id_column_name="our_object_subclass",
        a=1,
        b="Testing Abstract Methods",
        c=datetime.now(),
    )

    test_object_json = test_object.to_json()

    assert (
        test_object_json
        == '{"entity_name": "OurObjectSubclass", "id": 1, "kwargs": {"id_column_name": "our_object_subclass", "a": 1, "b": "Testing Abstract Methods", "c": "'  # noqa: E501
        + test_object.get("c").isoformat()
        + '"}}'
    )

    logger.end(TEST_TO_JSON_WITH_DATETIME_FUNCTION_NAME)


def test_to_json_dict():
    TEST_TO_JSON_DICT_FUNCTION_NAME = "test_to_json_dict"
    logger.start(TEST_TO_JSON_DICT_FUNCTION_NAME)

    test_object = OurObjectSubclass(
        entity_name=ENTITY_NAME,
        id=1,
        id_column_name="our_object_subclass",
        a=1,
        b="Testing Abstract Methods",
        c=datetime.now(),
    )

    json_object = {
        "entity_name": "OurObjectSubclass",
        "id": 1,
        "kwargs": {
            "id_column_name": "our_object_subclass",
            "a": 1,
            "b": "Testing Abstract Methods",
            "c": test_object.get("c").isoformat(),
            },
        }

    test_object_json = test_object.to_json_dict()

    assert test_object_json == json_object, (
        f"Expected: {json_object}, but got: {test_object_json}"
    )

    logger.end(TEST_TO_JSON_DICT_FUNCTION_NAME)


def test_to_json_str():
    TEST_TO_JSON_STR_FUNCTION_NAME = "test_to_json_str"
    logger.start(TEST_TO_JSON_STR_FUNCTION_NAME)

    test_object = OurObjectSubclass(
        id_column_name="our_object_subclass",
        a=1,
        b="Testing Abstract Methods",
        c=datetime.now(),
    )
    json_object = f'{{"entity_name": "OurObjectSubclass", "id": null, "kwargs": {{"id_column_name": "our_object_subclass", "a": 1, "b": "Testing Abstract Methods", "c": "{test_object.get("c").isoformat()}"}}}}'  # noqa: E501

    test_object_json = test_object.to_json_str()

    assert test_object_json == json_object, (
        f"Expected: {json_object}, but got: {test_object_json}"
    )

    logger.end(TEST_TO_JSON_STR_FUNCTION_NAME)


if __name__ == "__main__":
    # test_our_object()
    # test_abstract_methods()
    # test_to_json_with_datetime()
    test_to_json_dict()
    test_to_json_str()
