import inspect
from logger_local.LoggerLocal import Logger
from src.mini_logger import MiniLogger
from dotenv import load_dotenv
load_dotenv()


def test_logger_and_minilogger_signatures():
    shared_methods = [
        "start", "end", "debug", "info", "warning", "error", "exception"
    ]

    for method in shared_methods:
        mini_method = getattr(MiniLogger, method, None)
        logger_method = getattr(Logger, method, None)

        assert mini_method is not None, f"MiniLogger missing method: {method}"
        assert logger_method is not None, f"Logger missing method: {method}"

        mini_sig = inspect.signature(mini_method)
        logger_sig = inspect.signature(logger_method)

        mini_params = [(p.name, p.default) for p in mini_sig.parameters.values()
                       if p.name not in ("self", "cls")]
        logger_params = [(p.name, p.default) for p in logger_sig.parameters.values()
                         if p.name not in ("self", "cls")]

        assert mini_params == logger_params, (
            f"Signature mismatch in method '{method}':\n"
            f"  MiniLogger: {mini_params}\n"
            f"  Logger:     {logger_params}"
        )
