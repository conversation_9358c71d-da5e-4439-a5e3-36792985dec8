import os
import time
import pytest
from datetime import date, datetime, time as datetime_time, timedelta

from dotenv import load_dotenv

from src import utilities
from src.mini_logger import MiniLogger as logger

load_dotenv()

TEST_TIME_DELTA = timedelta(seconds=25853)
TEST_TIME_FORMAT = "07:10:53"
# dummy jwt for testing
TEST_USER_JWT = (
    "eyJhbGciObJIUzI1NiIsImR5cCI4DkpXWCJ9."
    "***********************************************"
    "6IjEyMzQ1Njc4IiwicHJvZmlsZUlkIjoiMTIzNDU2NzgiLCJy"
    "b2xlcyI6WyJVU0VSIl0sImlhdCI6MTYxNTI0ODQ2MSwiZXhwIjoxNjE1MjUyMDYxfQ."
    "SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
)


def test_timedelta_to_time_format():
    TEST_TIMEDELTA_TO_TIME_FORMAT_METHOD_NAME = "test_timedelta_to_time_format"
    logger.start(TEST_TIMEDELTA_TO_TIME_FORMAT_METHOD_NAME)

    time_format = utilities.timedelta_to_time_format(TEST_TIME_DELTA)
    assert time_format == TEST_TIME_FORMAT

    logger.end(TEST_TIMEDELTA_TO_TIME_FORMAT_METHOD_NAME)


def test_is_list_of_dicts():
    TEST_IS_LIST_OF_DICTS_METHOD_NAME = "test_is_list_of_dicts"
    logger.start(TEST_IS_LIST_OF_DICTS_METHOD_NAME)

    assert utilities.is_list_of_dicts([])
    assert utilities.is_list_of_dicts([{"a": 1}, {"b": 2}])
    assert not utilities.is_list_of_dicts([{"a": 1}, {"b": 2}, 3])
    assert not utilities.is_list_of_dicts([{"a": 1}, {"b": 2}, "c"])
    assert utilities.is_list_of_dicts([{"a": 1}, {"b": 2}, {"c": 3}])
    assert not utilities.is_list_of_dicts([{"a": 1}, {"b": 2}, {"c": {"d": 5}}, 4])

    logger.end(TEST_IS_LIST_OF_DICTS_METHOD_NAME)


def test_is_valid_time_range():
    TEST_IS_VALID_TIME_RANGE_METHOD_NAME = "test_is_valid_time_range"
    logger.start(TEST_IS_VALID_TIME_RANGE_METHOD_NAME)
    valid_time_range = (datetime_time(12, 34, 56), datetime_time(23, 45, 12))
    invalid_time_range = (
        datetime_time(12, 34, 56),
        datetime_time(11, 22).hour,
    )  # Invalid format

    assert utilities.is_valid_time_range(valid_time_range) is True
    assert utilities.is_valid_time_range(invalid_time_range) is False

    logger.end(TEST_IS_VALID_TIME_RANGE_METHOD_NAME)


def test_is_valid_date_range():
    TEST_IS_VALID_DATE_RANGE_METHOD_NAME = "test_is_valid_date_range"
    logger.start(TEST_IS_VALID_DATE_RANGE_METHOD_NAME)
    valid_date_range = (date(2023, 10, 26), date(2023, 10, 27))
    invalid_date_range = (
        date(2023, 10, 26),
        datetime_time(23, 45, 12),
    )  # Invalid format
    check_date_inside = date(2023, 10, 27)
    check_date_outside = date(2023, 10, 25)

    assert utilities.is_valid_date_range(valid_date_range) is True
    assert utilities.is_valid_date_range(invalid_date_range) is False
    assert utilities.is_date_in_date_range(check_date_inside, valid_date_range) is True
    assert (
        utilities.is_date_in_date_range(check_date_outside, valid_date_range) is False
    )

    logger.end(TEST_IS_VALID_DATE_RANGE_METHOD_NAME)


def test_is_valid_datetime_range():
    TEST_IS_VALID_DATETIME_RANGE_METHOD_NAME = "test_is_valid_datetime_range"
    logger.start(TEST_IS_VALID_DATETIME_RANGE_METHOD_NAME)
    valid_datetime_range = (
        datetime(2023, 10, 26, 12, 34, 56),
        datetime(2023, 10, 27, 23, 45, 12),
    )
    invalid_datetime_range = (
        datetime(2023, 10, 26, 12, 34, 56),
        datetime_time(23, 45, 12),
    )  # Invalid format
    check_datetime_inside = datetime(2023, 10, 27, 15, 0, 0)
    check_datetime_outside = datetime(2023, 10, 25, 5, 0, 0)

    assert utilities.is_valid_datetime_range(valid_datetime_range) is True
    assert utilities.is_valid_datetime_range(invalid_datetime_range) is False
    assert (
        utilities.is_datetime_in_datetime_range(
            check_datetime_inside, valid_datetime_range
        )
        is True
    )
    assert (
        utilities.is_datetime_in_datetime_range(
            check_datetime_outside, valid_datetime_range
        )
        is False
    )
    logger.end(TEST_IS_VALID_DATETIME_RANGE_METHOD_NAME)


# same test name
def test_timedelta_to_time_format_v2():
    TEST_TIMEDELTA_TO_TIME_FORMAT_METHOD_NAME = "test_timedelta_to_time_format"
    logger.start(TEST_TIMEDELTA_TO_TIME_FORMAT_METHOD_NAME)
    duration = timedelta(hours=2, minutes=30, seconds=45)
    formatted_time = utilities.timedelta_to_time_format(duration)
    assert formatted_time == "02:30:45"
    logger.end(TEST_TIMEDELTA_TO_TIME_FORMAT_METHOD_NAME)


def test_is_time_in_time_range():
    TEST_IS_TIME_IN_TIME_RANGE_METHOD_NAME = "test_is_time_in_time_range"
    logger.start(TEST_IS_TIME_IN_TIME_RANGE_METHOD_NAME)
    valid_time_range = (datetime_time(12, 34, 56), datetime_time(23, 45, 12))
    check_time_inside = datetime_time(15, 0, 0)
    check_time_outside = datetime_time(5, 0, 0)

    assert utilities.is_time_in_time_range(check_time_inside, valid_time_range) is True
    assert (
        utilities.is_time_in_time_range(check_time_outside, valid_time_range) is False
    )

    logger.end(TEST_IS_TIME_IN_TIME_RANGE_METHOD_NAME)


def test_is_date_in_date_range():
    TEST_IS_DATE_IN_DATE_RANGE_METHOD_NAME = "test_is_date_in_date_range"
    logger.start(TEST_IS_DATE_IN_DATE_RANGE_METHOD_NAME)
    valid_date_range = (date(2023, 10, 26), date(2023, 10, 27))
    invalid_date_range = (
        date(2023, 10, 26),
        datetime_time(23, 45, 12),
    )  # Invalid format
    check_date_inside = date(2023, 10, 27)
    check_date_outside = date(2023, 10, 25)

    assert utilities.is_date_in_date_range(check_date_inside, valid_date_range) is True
    assert (
        utilities.is_date_in_date_range(check_date_outside, valid_date_range) is False
    )
    assert (
        utilities.is_date_in_date_range(check_date_inside, invalid_date_range) is False
    )

    logger.end(TEST_IS_DATE_IN_DATE_RANGE_METHOD_NAME)


def test_is_datetime_in_datetime_range():
    TEST_IS_DATETIME_IN_DATETIME_RANGE_METHOD_NAME = (
        "test_is_datetime_in_datetime_range"
    )
    logger.start(TEST_IS_DATETIME_IN_DATETIME_RANGE_METHOD_NAME)
    valid_datetime_range = (
        datetime(2023, 10, 26, 12, 34, 56),
        datetime(2023, 10, 27, 23, 45, 12),
    )
    invalid_datetime_range = (
        datetime(2023, 10, 26, 12, 34, 56),
        datetime_time(23, 45, 12),
    )  # Invalid format
    check_datetime_inside = datetime(2023, 10, 27, 15, 0, 0)
    check_datetime_outside = datetime(2023, 10, 25, 5, 0, 0)

    assert (
        utilities.is_datetime_in_datetime_range(
            check_datetime_inside, valid_datetime_range
        )
        is True
    )
    assert (
        utilities.is_datetime_in_datetime_range(
            check_datetime_outside, valid_datetime_range
        )
        is False
    )
    assert (
        utilities.is_datetime_in_datetime_range(
            check_datetime_inside, invalid_datetime_range
        )
        is False
    )

    logger.end(TEST_IS_DATETIME_IN_DATETIME_RANGE_METHOD_NAME)


def test_getters():
    # TODO Assign to environment_name and brand_name and check the values
    utilities.get_environment_name()
    utilities.get_brand_name()


def test_append_if_not_exist():
    lst = [1, 2, 3]
    utilities.append_if_not_exist(lst, 4)
    assert lst == [1, 2, 3, 4]
    utilities.append_if_not_exist(lst, 4)
    assert lst == [1, 2, 3, 4]


def test_to_dict():
    assert utilities.to_dict(None) == {}
    assert utilities.to_dict({"a": 1}) == {"a": 1}
    assert utilities.to_dict('{"a": 1}') == {"a": 1}


def test_to_json():
    assert utilities.to_json(None) == "{}"
    assert utilities.to_json({"a": 1}) == '{"a": 1}'
    assert utilities.to_json('{"a": 1}') == '{"a": 1}'


def test_our_get_env():
    dummy_key = "DUMMY_KEY"
    dummy_value = "DUMMY_VALUE"
    with pytest.raises(Exception):
        utilities.our_get_env(dummy_key, raise_if_not_found=True)
    assert utilities.our_get_env(dummy_key, raise_if_not_found=False) is None
    assert utilities.our_get_env(dummy_key, default=dummy_value) == dummy_value

    assert utilities.our_get_env(dummy_key, default="", raise_if_empty=False) == ""
    assert utilities.our_get_env(dummy_key, default="", raise_if_not_found=False) == ""
    with pytest.raises(Exception):
        utilities.our_get_env(dummy_key, default="", raise_if_empty=True)

    os.environ[dummy_key] = ""
    with pytest.raises(Exception):
        utilities.our_get_env(dummy_key, raise_if_empty=True)
    with pytest.raises(Exception):
        utilities.our_get_env(dummy_key, raise_if_not_found=False, raise_if_empty=True)
    assert utilities.our_get_env(dummy_key, raise_if_empty=False) == ""

    os.environ[dummy_key] = dummy_value
    assert utilities.our_get_env(dummy_key) == dummy_value


def test_encode_decode_jwt():
    payload = {"user_id": 123, "username": "john_doe", "exp": time.time() + 3600}
    private_key = "secret"
    token = utilities.encode_jwt(payload, private_key)
    decoded_payload = utilities.decode_jwt(token, private_key)
    assert payload == decoded_payload

    try:
        utilities.decode_jwt(token, "wrong_key")
        assert False, "Should have raised an exception"
    except Exception:  # as e:
        # TODO Can we have an additional except statement for this specific exception called InvalidSignatureError?
        assert True  # raise InvalidSignatureError("Signature verification failed")


def test_obfuscate_log_dict():
    # TODO environment_name =
    environment = utilities.get_environment_name()
    os.environ["ENVIRONMENT_NAME"] = "prod1"

    log_dict = {
        "user_email": "<EMAIL>",
        "user_password": "supersecret",
        "login_time": "2024-06-03T12:34:56",
        "user_name": "John Doe",
    }
    expected_output = {
        "user_email": "***",
        "user_password": "***",
        "login_time": "2024-06-03T12:34:56",
        "user_name": "***",
    }
    assert utilities.obfuscate_log_dict(log_dict) == expected_output

    log_dict = {
        "user_email": "<EMAIL>",
        "details": {
            "token": "abcdef123456",
            "address": "123 Main St",
            "phone_number": "************",
        },
        "component_id": 3,
    }
    expected_output = {
        "user_email": "***",
        "details": {"token": "***", "address": "***", "phone_number": "***"},
        "component_id": 3,
    }
    assert utilities.obfuscate_log_dict(log_dict) == expected_output

    log_dict = {
        "login_time": "2024-06-03T12:34:56",
        "status": "success",
        "session_id": "xyz123",
    }
    expected_output = {
        "login_time": "2024-06-03T12:34:56",
        "status": "success",
        "session_id": "xyz123",
    }
    assert utilities.obfuscate_log_dict(log_dict) == expected_output

    log_dict = {
        "user_email": "<EMAIL>",
        "login_time": "2024-06-03T12:34:56",
        "details": {
            "token": "abcdef123456",
            "address": "123 Main St",
            "name_last": "Doe",
            "test": "test",
        },
    }
    expected_output = {
        "user_email": "***",
        "login_time": "2024-06-03T12:34:56",
        "details": {
            "token": "***",
            "address": "***",
            "name_last": "***",
            "test": "test",
        },
    }
    assert utilities.obfuscate_log_dict(log_dict) == expected_output

    os.environ["ENVIRONMENT_NAME"] = environment


def test_remove_digits():
    string = "ab12cd34"
    actual = utilities.remove_digits(string)
    expected = "abcd"
    assert actual == expected


def test_generate_otp():
    otp = utilities.generate_otp()
    assert isinstance(otp, int)
    assert len(str(otp)) == 6
    assert 100000 <= otp <= 999999


def test_current_datetime_string():
    output = utilities.get_current_datetime_string()
    assert isinstance(output, str)


def test_datetime_from_str():
    date_str = "2025-07-04 14:45:00"
    output = utilities.datetime_from_str(date_str)
    assert isinstance(output, datetime)


def test_datetime_to_str():
    input_datetime = datetime(2025, 7, 4, 14, 45, 0)
    output = utilities.datetime_to_str(input_datetime)
    assert isinstance(output, str)
    assert output == "2025-07-04 14:45:00"


def test_validate_arguments():
    args = {"company": "circlez", "number": None}
    with pytest.raises(ValueError):
        utilities.validate_arguments(args)


def test_reformat_time_string():
    input_str = "07050020"
    expected = "07:05:00:20"
    actual = utilities.reformat_time_string(input_str)
    assert expected == actual


def test_snake_to_camel():
    assert utilities.snake_to_camel("convert_test_string") == "convertTestString"


def test_camel_to_snake():
    assert utilities.camel_to_snake("convertTestString") == "convert_test_string"
