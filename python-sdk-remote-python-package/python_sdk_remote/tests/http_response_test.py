import json
import traceback
from http import HTTPStatus

from src.http_response import (
    # TODO we use event terminology for something else. Can we change event here to http_request with backward compatibility using logger.deprecated()  # noqa E501
    get_payload_dict_from_event,
    get_path_parameters_dict_from_event,
    get_query_string_parameters_from_event,
    create_authorization_http_headers,
    get_user_jwt_from_event,
    create_return_http_headers,
    create_error_http_response,
    create_ok_http_response,
)


def test_valid_event():
    test_valid_event = {
        "body": '{"test_body": "test_body"}',
        "pathParameters": {"test_path": "test_path"},
        "queryStringParameters": {"test_query": "test_query"},
        "headers": {
            "Content-Type": "application/json",
            "Authorization": "Bearer test_jwt",
        },
    }
    assert get_payload_dict_from_event(test_valid_event) == {"test_body": "test_body"}
    assert get_path_parameters_dict_from_event(test_valid_event) == {
        "test_path": "test_path"
    }
    assert get_query_string_parameters_from_event(test_valid_event) == {
        "test_query": "test_query"
    }
    assert get_user_jwt_from_event(test_valid_event) == "test_jwt"


def test_missing_event():
    test_missing_event = {}
    assert get_payload_dict_from_event(test_missing_event) == {}
    assert get_path_parameters_dict_from_event(test_missing_event) == {}
    assert get_query_string_parameters_from_event(test_missing_event) == {}


def test_http():
    assert create_authorization_http_headers("test") == {
        "Content-Type": "application/json",
        "Authorization": "Bearer test",
    }
    assert create_return_http_headers() == {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
    }
    exception = Exception("test_error")
    assert create_error_http_response(exception) == {
        "statusCode": HTTPStatus.BAD_REQUEST.value,
        "headers": {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
        },
        "body": json.dumps({"error": "test_error"}),
        "traceback": traceback.format_exc(),
    }

    assert create_ok_http_response({"test_body": "test_body"}) == {
        "statusCode": HTTPStatus.OK.value,
        "headers": {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
        },
        "body": '{"test_body": "test_body"}',
    }
