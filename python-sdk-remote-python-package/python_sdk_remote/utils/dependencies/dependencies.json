{"action-items-local": ["contact-notes-local"], "age-detection-local": [], "api-management-local": ["google-contact-local", "message-local", "whatsapp-message-vonage-local"], "bert-local": [], "business-profile-facebook-local": [], "business-profile-yelp-local": [], "campaign-local": [], "community-waiting-list-local": [], "contact-email-address-local": ["contact-person-profile-csv-imp-local", "google-contact-local"], "contact-group-local": ["contact-location-local", "contact-notes-local", "contact-person-profile-csv-imp-local", "google-contact-local"], "contact-local": ["contact-group-local", "contact-person-profile-csv-imp-local", "contact-persons-local", "google-contact-local"], "contact-location-local": ["contact-person-profile-csv-imp-local", "google-contact-local"], "contact-notes-local": ["contact-person-profile-csv-imp-local", "google-contact-local"], "contact-person-profile-csv-imp-local": [], "contact-persons-local": ["contact-person-profile-csv-imp-local", "contact-profile-local", "google-contact-local"], "contact-phone-local": ["contact-person-profile-csv-imp-local", "google-contact-local"], "contact-profile-local": ["contact-person-profile-csv-imp-local", "google-contact-local"], "contact-user-external-local": ["contact-person-profile-csv-imp-local", "google-contact-local"], "criteria-local": ["message-local"], "data-source-local": ["profile-reddit-restapi-imp-local"], "database-infrastructure-local": ["contact-notes-local", "database-mysql-local", "gender-detection-local", "labels-local", "location-local", "smartlink-local", "user-external-local", "user-local"], "database-mysql-local": ["action-items-local", "api-management-local", "campaign-local", "contact-email-address-local", "contact-group-local", "contact-local", "contact-location-local", "contact-notes-local", "contact-person-profile-csv-imp-local", "contact-persons-local", "contact-phone-local", "contact-profile-local", "contact-user-external-local", "criteria-local", "data-source-local", "dialog-workflow-local", "email-address-local", "email-message-aws-ses-local", "entity-type-local", "event-external-local", "gender-local", "google-account-local", "google-contact-local", "group-local", "importer-local", "internet-domain-local", "job-local", "label-message-local", "location-local", "location-profile-local", "machine-learning-model-local", "message-local", "message-send-local", "operational-hours-local", "organization-profile-local", "organizations-local", "people-local", "person-local", "phones-local", "profile-local", "profile-metrics-local", "profile-profile-local", "profile-reaction-local", "profile-url-local", "profile-user-local", "queue-local", "queue-worker-local", "reaction-local", "real-estate-realtor-com-imp-local", "smartlink-local", "sms-message-aws-sns-local", "sms-message-inforu-local", "sql-to-code-local", "star-local", "storage-local", "text-block-local", "user-external-local", "user-local", "variable-local", "whatsapp-message-vonage-local"], "dialog-workflow-local": [], "email-address-local": ["contact-email-address-local", "profile-local", "user-local"], "email-message-aws-ses-local": ["messages-local"], "entity-type-local": ["profile-reddit-restapi-imp-local", "real-estate-realtor-com-imp-local"], "event-external-local": ["event-remote"], "event-meetup-com-selenium-imp-local": [], "event-remote": ["event-ticketmaster-graphql-imp-local"], "event-ticketmaster-graphql-imp-local": [], "gender-detection-local": [], "gender-local": ["gender-detection-local", "profile-local"], "google-account-local": ["google-contact-local"], "google-contact-local": [], "group-local": ["contact-group-local", "contact-location-local", "labels-local", "people-local"], "group-profile-remote": ["profile-local"], "group-remote": ["contact-group-local", "profile-local", "profile-reddit-restapi-imp-local"], "importer-local": ["contact-person-profile-csv-imp-local", "google-contact-local", "profile-reddit-restapi-imp-local", "real-estate-realtor-com-imp-local"], "internet-domain-local": ["contact-person-profile-csv-imp-local", "contact-user-external-local", "google-contact-local"], "item-local": ["message-local", "messages-local"], "job-local": ["group-local"], "label-message-local": ["messages-local"], "labels-local": [], "language-remote": ["action-items-local", "contact-email-address-local", "contact-group-local", "contact-location-local", "contact-notes-local", "contact-profile-local", "data-source-local", "database-mysql-local", "dialog-workflow-local", "email-address-local", "gender-local", "group-local", "group-remote", "job-local", "location-local", "location-profile-local", "message-local", "organizations-local", "person-local", "profile-facebook-selenium-scraper-imp-local", "profile-local", "profile-zoominfo-graphql-imp-local", "reaction-local", "user-context-remote", "variable-local"], "location-local": ["contact-location-local", "contact-person-profile-csv-imp-local", "google-contact-local", "importer-local", "organizations-local", "profile-local", "profile-reddit-restapi-imp-local", "real-estate-realtor-com-imp-local", "user-local"], "location-profile-local": ["profile-local"], "logger-local": ["action-items-local", "api-management-local", "bert-local", "business-profile-yelp-local", "campaign-local", "contact-email-address-local", "contact-group-local", "contact-local", "contact-location-local", "contact-notes-local", "contact-person-profile-csv-imp-local", "contact-persons-local", "contact-phone-local", "contact-profile-local", "contact-user-external-local", "criteria-local", "data-source-local", "database-infrastructure-local", "database-mysql-local", "dialog-workflow-local", "email-address-local", "email-message-aws-ses-local", "entity-type-local", "event-external-local", "event-remote", "event-ticketmaster-graphql-imp-local", "gender-detection-local", "gender-local", "google-account-local", "google-contact-local", "group-local", "group-profile-remote", "group-remote", "importer-local", "internet-domain-local", "item-local", "job-local", "label-message-local", "language-remote", "location-local", "location-profile-local", "machine-learning-model-local", "message-local", "message-send-local", "operational-hours-local", "organization-profile-local", "people-local", "person-local", "phones-local", "profile-facebook-selenium-scraper-imp-local", "profile-instagram-local", "profile-local", "profile-metrics-local", "profile-profile-local", "profile-reaction-local", "profile-reddit-restapi-imp-local", "profile-url-local", "profile-user-local", "profile-zoominfo-graphql-imp-local", "queue-local", "queue-worker-local", "reaction-local", "real-estate-realtor-com-imp-local", "smartlink-local", "smartlink-remote-restapi", "sms-message-aws-sns-local", "sms-message-inforu-local", "sql-to-code-local", "star-local", "storage-local", "text-block-local", "unified-json-api", "user-external-local", "user-local", "variable-local", "whataspp-message-inforu-local", "whatsapp-message-vonage-local"], "machine-learning-model-local": [], "message-local": ["dialog-workflow-local", "email-message-aws-ses-local", "message-send-local", "messages-local", "smartlink-local", "sms-message-aws-sns-local", "sms-message-inforu-local", "whataspp-message-inforu-local", "whatsapp-message-vonage-local"], "message-send-local": [], "messages-local": ["message-send-local"], "operational-hours-local": ["profile-local"], "organization-profile-local": ["contact-person-profile-csv-imp-local", "google-contact-local"], "organizations-local": ["contact-person-profile-csv-imp-local", "google-contact-local"], "people-local": ["person-local"], "person-local": ["contact-local", "contact-persons-local", "profile-local", "text-block-local"], "phones-local": ["contact-phone-local", "message-local"], "profile-facebook-selenium-scraper-imp-local": [], "profile-instagram-local": [], "profile-linkedin-graphql-imp-local": [], "profile-local": ["contact-notes-local", "contact-profile-local", "message-local", "profile-facebook-selenium-scraper-imp-local", "profile-instagram-local", "profile-reddit-restapi-imp-local", "profile-zoominfo-graphql-imp-local", "text-block-local"], "profile-local-reddit-restapi-imp-local": [], "profile-metrics-local": [], "profile-profile-local": ["profile-local"], "profile-reaction-local": ["profile-local"], "profile-reddit-restapi-imp-local": [], "profile-url-local": [], "profile-user-local": [], "profile-zoominfo-graphql-imp-local": [], "python-sdk-local": ["profile-instagram-local"], "python-sdk-remote": ["action-items-local", "api-management-local", "business-profile-yelp-local", "contact-group-local", "contact-person-profile-csv-imp-local", "database-infrastructure-local", "database-mysql-local", "dialog-workflow-local", "email-message-aws-ses-local", "event-external-local", "event-remote", "event-ticketmaster-graphql-imp-local", "google-account-local", "google-contact-local", "group-profile-remote", "group-remote", "labels-local", "location-local", "location-profile-local", "logger-local", "people-local", "profile-facebook-selenium-scraper-imp-local", "profile-local", "profile-reddit-restapi-imp-local", "profile-zoominfo-graphql-imp-local", "python-sdk-local", "smartlink-local", "smartlink-remote-restapi", "sms-message-aws-sns-local", "sms-message-inforu-local", "star-local", "storage-local", "storage-remote-graphql", "user-context-remote", "user-external-local", "whataspp-message-inforu-local", "whatsapp-message-vonage-local"], "queue-local": ["queue-worker-local"], "queue-worker-local": ["messages-local", "smartlink-local"], "reaction-local": ["profile-local"], "real-estate-realtor-com-imp-local": [], "recruitment-employer-local": [], "recruitment-employer-monster-com-indeed-com-vacancy-scraper-local": [], "smartlink-local": ["smartlink-remote-restapi", "variable-local"], "smartlink-remote-restapi": ["variable-local"], "sms-message-aws-sns-local": ["messages-local"], "sms-message-inforu-local": [], "sql-to-code-local": [], "star-local": ["api-management-local", "message-local"], "storage-local": ["internet-domain-local", "profile-local"], "storage-remote-graphql": [], "text-block-local": ["contact-notes-local"], "unified-json-api": [], "url-remote": ["api-management-local", "contact-person-profile-csv-imp-local", "database-mysql-local", "event-remote", "google-contact-local", "group-profile-remote", "group-remote", "python-sdk-local", "python-sdk-remote", "smartlink-remote-restapi", "storage-remote-graphql", "user-context-remote"], "user-context-remote": ["action-items-local", "contact-email-address-local", "contact-location-local", "contact-notes-local", "contact-person-profile-csv-imp-local", "contact-profile-local", "contact-user-external-local", "dialog-workflow-local", "event-remote", "google-contact-local", "group-local", "group-profile-remote", "group-remote", "labels-local", "labels-local", "location-local", "logger-local", "organizations-local", "person-local", "profile-instagram-local", "profile-local", "profile-reddit-restapi-imp-local", "queue-local", "smartlink-remote-restapi", "star-local", "storage-local", "text-block-local", "user-external-local"], "user-external-local": ["contact-person-profile-csv-imp-local", "contact-user-external-local", "google-account-local", "google-contact-local", "profile-instagram-local"], "user-local": [], "variable-local": ["dialog-workflow-local", "message-local"], "visibility-local": ["profile-local"], "whataspp-message-inforu-local": ["messages-local"], "whatsapp-message-vonage-local": ["messages-local"]}