# TODO: add readme
# TODO Should be in the same location of the Powershell to freeze the Python packages versions in the requirments.txt

import json
import os
# pip install BeautifulSoup4 requests importlib-metadata
import subprocess
import sys
from collections import defaultdict
from typing import Dict, List
from typing import Union

import importlib_metadata
import requests
from bs4 import BeautifulSoup

full_current_file_path = os.path.dirname(os.path.abspath(__file__))
save_at_folder = os.path.join(full_current_file_path, 'dependencies')
os.makedirs(save_at_folder, exist_ok=True)


def install(packages):
    print("Installing...", packages)
    if isinstance(packages, str):
        packages = [packages]
    # for package in packages:
    #     # We can't install multiple packages in a single command, because pip will stop at the first error
    #     subprocess.run([sys.executable, "-m", "pip", "install", "-U", "--no-deps", package], check=False)
    subprocess.run([sys.executable, "-m", "pip", "install", "-U", "--no-deps"] + packages, check=False)


def get_dependencies_per_package(include_packages: list) -> Dict[str, List[str]]:
    dependencies_per_package = defaultdict(list)
    for dist in importlib_metadata.distributions():
        package_name = dist.metadata['Name']
        if package_name not in include_packages:
            continue

        dependencies = dist.metadata.get_all('Requires-Dist', [])
        for dependency in dependencies:
            depend_on = dependency.split()[0]
            if depend_on in include_packages and depend_on not in dependencies_per_package[package_name]:
                dependencies_per_package[depend_on].append(package_name)

    for package in include_packages:
        if package not in dependencies_per_package:
            dependencies_per_package[package] = []
    return dependencies_per_package


def get_included_packages() -> List[str]:
    url = "https://pypi.org/user/circles/"
    class_name = "package-snippet__title"

    response = requests.get(url)
    soup = BeautifulSoup(response.text, "html.parser")
    package_names = soup.find_all("h3", class_=class_name)
    included_packages = [package_name.text for package_name in package_names]
    return included_packages


def build_dependency_tree(
        dependencies_per_package: Dict[str, List[str]]) -> List[Union[str, Dict[str, List[Union[str, Dict]]]]]:
    called = set()

    def build_tree(package: str) -> Union[str, Dict[str, List[Union[str, Dict]]]]:
        if package in called:
            return package
        called.add(package)
        if package not in dependencies_per_package:
            return package
        build_tree_result = {package: [build_tree(dep) for dep in dependencies_per_package[package]]}
        return build_tree_result

    trees = []
    for package in dependencies_per_package:
        trees.append(build_tree(package))

    return trees


printed = []


def display_dependency_tree(tree: Union[str, Dict[str, List[Union[str, Dict]]]], indent: int = 0) -> None:
    if isinstance(tree, dict):
        for package, dependencies in tree.items():
            if package not in printed:
                print("-" * indent + package)
                printed.append(package)
                for dep in dependencies:
                    display_dependency_tree(dep, indent + 1)
    else:
        if tree not in printed:
            print("-" * (indent + 1) + tree)
            printed.append(tree)


def sort_values(dependencies_per_package: Dict[str, List[str]]) -> Dict[str, List[str]]:
    sort_values_result = {package: sorted(dependencies) for package, dependencies in dependencies_per_package.items()}
    return sort_values_result


def main(skip_install: bool = False):
    # Add here outdated packages that can't be installed  otherwise pip raise
    remove = ["CirclesS3Storage", "age-detection-local-python-package", "rest-api-vacancy-scraper-local",
              'recruitment-employer-local-python-package', 'local-logger-python-backend',
              'local-age-detection-python-backend']
    add = ['python-sdk-local']  # outdated packages that are not in pypi, but you still want to see the dependencies
    print()
    save_at = os.path.join(save_at_folder, 'dependencies.json')
    if skip_install and os.path.exists(save_at):
        print("getting from dependencies.json")
        with open(save_at, 'r') as f:
            dependencies_per_package = json.load(f)
        dependencies_per_package = sort_values(dependencies_per_package)
        with open(save_at, 'w') as f:
            json.dump(dependencies_per_package, f, indent=2, sort_keys=True)
    else:
        include_packages = get_included_packages()
        include_packages = [package for package in include_packages if package not in remove]
        install(include_packages)
        include_packages += add
        dependencies_per_package = get_dependencies_per_package(include_packages)
        dependencies_per_package = sort_values(dependencies_per_package)
        print("writing to dependencies.json")
        with open(save_at, 'w') as f:
            json.dump(dependencies_per_package, f, indent=2, sort_keys=True)

    print("num of edges per node, sorted by num of edges:")
    for package, dependencies in sorted(dependencies_per_package.items(), key=lambda x: len(x[1]), reverse=True):
        print(f"{package}: {len(dependencies)}")

    # clean dependencies_per_package before plotting
    too_popular = ["logger-local", "database-mysql-local", "python-sdk-remote", "url-remote", "user-context-remote",
                   "language-remote", "database-infrastructure-local"]
    remove += too_popular
    for package in list(dependencies_per_package.keys()):
        if package in remove:
            dependencies_per_package.pop(package)
        else:
            dependencies_per_package[package] = [dep for dep in dependencies_per_package[package]
                                                 if dep not in remove]

    # Simple print in ASCII format:
    # dependency_trees = build_dependency_tree(dependencies_per_package)
    # for tree in dependency_trees:
    #     display_dependency_tree(tree)

    plot_graph(dependencies_per_package)


def plot_graph(dependencies_per_package: Dict[str, List[str]]):
    # pip install networkx matplotlib scipy
    import networkx as nx
    import matplotlib.pyplot as plt

    # Create a directed graph
    G = nx.DiGraph()

    # Add nodes (packages) to the graph
    for package in dependencies_per_package:
        G.add_node(package)

    # Add edges (dependencies) to the graph
    for package, dependencies in dependencies_per_package.items():
        for dep in dependencies:
            G.add_edge(dep, package)

    # Option to use weight_func:
    # max_len = max(len(dependencies) for dependencies in dependencies_per_package.values())
    # def weight_func(v, u, e):
    #     return  0.2 * (len(dependencies_per_package.get(v, [])) + len(dependencies_per_package.get(u, [])))
    # error: nx.planar_layout, nx.planar_layout, nx.bipartite_layout, nx.rescale_layout, nx.multipartite_layout
    for layout in (nx.kamada_kawai_layout, nx.circular_layout, nx.shell_layout,
                   nx.random_layout, nx.spring_layout, nx.spiral_layout):
        pos = layout(G)  # , weight=weight_func)

        # Draw the graph
        plt.figure(figsize=(20, 15))  # Increase the figure size
        nx.draw(G, pos, with_labels=True, node_color="skyblue", font_size=6, font_color="black", node_size=1000,
                width=1,
                edge_color="gray", arrowsize=5)
        plt.axis("off")  # Hide the axes
        plt.tight_layout()  # Adjust the layout to fit the figure

        # Save the plot as an image file
        path = os.path.join(save_at_folder, f"dependencies_tree_{layout.__name__}.png")
        plt.savefig(path, dpi=300, bbox_inches="tight")


if __name__ == "__main__":
    main()  # TODO: allow CLI arguments
