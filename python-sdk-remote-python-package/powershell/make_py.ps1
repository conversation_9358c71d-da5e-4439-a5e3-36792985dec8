# We better clean so it will be easier to see the errors of the current execution (should be in the beginning of the script)
Clear-Host

# TODO Add the version from setup.py
Write-Host "make_py.ps1 (From python-sdk-remote)"

$pythonExecutable="python3.12"
$pythonExecutable="python3" # 3.10

# Analyze the current directory
$currentDirectory = (Split-Path $PWD.Path -Leaf)
# Write-Host "Current directory=" $currentDirectory

$packageName = $currentDirectory -replace '-python-package$', '' `
                    -replace '-', '_'
                    #| ForEach-Object { $_.ToLower() }
Write-Host "Package name/directory=" $packageName

# Before git pull
# "There is no =="
if ( $Args.Count -eq 0 ) {
    Write-Host "No parameters, doing all default steps"
    # TODO Change all to true
    # TODO GitPull should be false when changing the directory structure (i.e. logger-local-python-package)
    $isGitPull=$false
    $isGitPullOriginDev=$false
    Write-Host "TODO not doing git pull !!!!! (as it is problematic when we change the directory structure i.e. logger-local-python-package)"
    $build=$true
    $lint=$true
    $test=$true
    $checkDuplicates=$false
}
else {
    Write-Host "number of arguments are " $args.Count
    Write-Host "Arguments are "$args
    ForEach ($arg in $args){
        write-output $arg
        if ($arg -eq "build") {
            $build=$true
        }
        if ($arg -eq "lint") {
            $lint=$true
        }
        if ($arg -eq "test" -or $arg -eq "tests" -or $arg -eq "t") {
            $test=$true
            Write-Host "Will test $args[1]"
            if ($args[1]) {
                # TODO Works only if using "test filename"
                $importantTests = $args[2]
            }
        }
        if ($arg -eq "duplicates" -or $arg -eq "dup") {
            $checkDuplicates=$true
        }
        if ($arg -eq "cleanup" -or $arg -eq "clean") {
            $cleanup=$true
        }        
    }
}

# Call GitPull.ps1 with appropriate switches based on flags
if ($isGitPull -and $isGitPullOriginDev) {
    & GitPull.ps1 -All
}
elseif ($isGitPull) {
    & GitPull.ps1 -CurrentBranch
}
elseif ($isGitPullOriginDev) {
    & GitPull.ps1 -OriginDev
}

$requiredFile="requirements.txt"
# Changing the current directory
if (-Not (Test-Path -Path $requiredFile)) {
    # Maybe someone fixed the directory structure in dev branch, so we need to pull it before we try to change the directory
    #git pull origin dev
    Write-Host "Didn't find $requiredFile in the current directory. Trying to change to the repo project directory $currentDirectory"
    if (Test-Path -Path $currentDirectory) {
        Write-Host "Found $currentDirectory. Changing to it."
        Set-Location $currentDirectory
    } else {
        Write-Host "Error: $currentDirectory not found. Exiting script."
        exit 1
    }
}
else {
    Write-Host "Found $requiredFile in the current directory. We are in the right directory."
}
Write-Host "Current directory=" $PWD.Path
# Pause

if (-Not (Test-Path -Path $requiredFile)) {
    Write-Host "Error: $requiredFile not found. You are not in the right directory. Exiting script."
    exit 1
}
else {
    Write-Host "Found $requiredFile in the current directory. We are in the right directory."
}

$environmentName="play1"

# After Set-Location (Change Directory)
$envFilename=".env."+$environmentName

# Option #1
# Load environment variables manually from .env.play1 (so we avoid using dotenv and we can capture the $LASTEXITCODE)
if (Test-Path -Path $envFilename) {
    (Get-Content $envFilename) -replace '^\s*#.*', '' | ForEach-Object {
        if ($_ -match '^\s*([^=]+?)\s*=\s*(.+)\s*$') {
            [System.Environment]::SetEnvironmentVariable($matches[1], $matches[2])
        }
    }   
    Write-Host "Loaded environment variables from $envFilename"
}
else {
    Write-Host "File $envFilename does not exist- Can load the environment variables from it."
}
$dotenvCommand=""

# Option #2 (problematic to capture the $LASTEXITCODE of the pytest)
# $dotenvCommand="dotenv -f $envFilename run -- "


# TODO Please add here all the tests which are problematic and you want to focus on them
if ($currentDirectory-eq "database-redis-local-python-package") {
    $importantTests = ("database_redis_local/tests/generic_crud_redis_test.py")
} elseif ($currentDirectory -eq "database-mysql-local-python-package") {
    # $importantTests = ("database_mysql_local/tests/generic_crud_mysql_test.py")
    # $importantTests = ("database_mysql_local/tests/generic_crud_mysql_test.py::test_insert_and_select")
    # $importantTests = ("database_mysql_local/tests/generic_crud_test.py::test_insert_and_select")
    # $importantTests = ("database_mysql_local/tests/generic_crud_ml_test.py::test_add_value_exist",
    # "database_mysql_local/tests/generic_crud_ml_test.py::test_foreach_with_select_function",
    # "database_mysql_local/tests/utils_test.py::test_get_entity_type_id_by_table_name")
    # $importantTests = ("database_mysql_local/tests/generic_crud_test.py::test_duplicate_insert")
    # $importantTests = ("database_mysql_local/tests/generic_crud_ml_test.py")
    # $importantTests = ("database_mysql_local/tests/generic_crud_ml_test.py::test_foreach_with_select_function",
    #     "database_mysql_local/tests/generic_crud_ml_test.py::test_upsert_value_with_abbreviations")
    # $importantTests = ("database_mysql_local/tests/generic_crud_ml_test.py::test_foreach_with_select_function")
    # $importantTests = ("database_mysql_local/tests/delete_test_data_test.py::test_delete_test_county_data")
    # $importantTests = ("database_mysql_local/tests/generic_crud_mysql_test.py::test_foreach_without_id")
    # $importantTests = ("database_mysql_local/tests/generic_crud_ml_test.py::test_add_value_not_exist")
    # $importantTests = ("database_mysql_local/tests/utils_test.py::test_get_entity_type_by_table_name")
} elseif ($currentDirectory -eq "smart-datastore-local-python-package") {
    $importantTests = ("smart_datastore_local/tests/smart_datastore_test.py")
# Those tests didn't pass, so I commented them out
# } elseif ($currentDirectory -eq "contact-main-local-python-package") {
#     $importantTests = ("contact_local/tests/contact_test.py::TestContactLocal::test_contactslocal_insert_and_contactslocal_get_contact_by_contact_id",
#     "contact_local/tests/contact_test.py::TestContactLocal::test_delete_contact",
#     "contact_local/tests/contact_test.py::TestContactLocal::test_insert_contact_object")
#     # without the TestContactLocal
#     $importantTests = ("contact_local/tests/contacts_local_test.py::test_contactslocal_insert_and_contactslocal_get_contact_by_contact_id",
#     "contact_local/tests/contacts_local_test.py::test_delete_contact",
#     "contact_local/tests/contacts_local_test.py::test_insert_contact_object")
} elseif ($currentDirectory -eq "logger-local-python-package") {
    # $importantTests = ("logger_local/tests/test_output_mode.py::test_minimum_severity")
    # $importantTests = ("logger_local/tests/test_exception.py::test_two_exceptions")
    # $importantTests = ("logger_local/tests/test_exception.py::test_one_exception")
    # $importantTests = ("logger_local/tests/test_module_wrapper.py::TestModuleWrapper")
    $importantTests = ("logger_local/tests/test_logger.py::test_check_function")
                    #   "logger_local/tests/test_logger.py::test_check_init_two_different_loggers")
} elseif ($currentDirectory -eq "user-context-remote-python-package") {
    $importantTests = ("user_context_remote/tests/test_user_context.py::test_user_context_login_by_user_jwt")
} else {
    #$importantTests = ("$packageName/tests/*.py")
}

# Which packages we want to Monitor
if ($currentDirectory-eq "database-redis-local-python-package") {
    $dependencyPackagesToMonitor=("database-infrastructure-local")
} elseif ($currentDirectory -eq "database-mysql-local-python-package") {
    $dependencyPackagesToMonitor=("database-infrastructure-local")
} elseif ($currentDirectory -eq "smart-datastore-local-python-package") {
    $dependencyPackagesToMonitor=("database-infrastructure-local")
} else {
    Write-Host "No dependencyPackagesToMonitor defined for $currentDirectory"
}

# TODO This should be based on the value setup in the user's profile.ps1 and not hard coded
if ($IsWindows) {
    $OrganizationReposDirectory = "d:\repos\circlez"
} else {
    $OrganizationReposDirectory = "~/repos/circlez"
}

# TODO I think the Set-Alias is not working in the shell executing the script
# Set-Alias make_py ..\..\python-sdk-remote-python-package\python-sdk-remote-python-package\bin\make_py.ps1
# Set-Alias make_py ..\..\python-sdk-remote-python-package\python-sdk-remote-python-package\bin\make_py.ps1
if ($IsWindows) {
    # Old
    Set-Alias make_py 'd:\repos\circles\python-sdk-remote-python-package\python-sdk-remote-python-package\bin\make_py.ps1'
    # New
    $makePyScript = Join-Path $OrganizationReposDirectory "python-sdk-remote-python-package\python-sdk-remote-python-package\bin\make_py.ps1"
} else {
    Set-Alias make_py '~/repos/circles/python-sdk-remote-python-package/python-sdk-remote-python-package/powershell/make_py.ps1'
    $makePyScript = Join-Path $OrganizationReposDirectory "python-sdk-remote-python-package/python-sdk-remote-python-package/powershell/make_py.ps1"
}
Set-Alias make_py2 $makePyScript

# We prefer one venv per organization to all repos to save disk space
$venvDirectory = "$OrganizationReposDirectory/.venv_circlez"
# For criteria-local-python-package need Python 3.11
$venvDirectory = "$OrganizationReposDirectory/.venv_circlez_3_13_3"
Write-Host "venv directory=" $venvDirectory

# TODO We should remove this from the general script
# $PackageRepo = "profile-reddit-restapi-imp-local-python-package"

$ErrorOccurred = $false

Set-Alias make_py "$OrganizationReposDirectory\python-sdk-remote-python-package\python-sdk-remote-python-package\bin\make_py.ps1"

# Shall we add Delete __pycache__ here?

# TODO If we don't need this function let's delete it
# Create version.py file based on setup.py - I'm not sure we need it if we can use __version__
# function Create-VersionFile {
#     if (Test-Path "setup.py") {
#         $setupContent = Get-Content "setup.py" -Raw
#         $versionMatch = [regex]::Match($setupContent, "version\s*=\s*['\"]([^'\"]+)['\"]")
        
#         if ($versionMatch.Success) {
#             $version = $versionMatch.Groups[1].Value
#             $versionFileContent = "__version__ = '$version'"
            
#             # Create version.py in the package directory
#             $versionFilePath = "$packageName/src/version.py"
#             if (Test-Path "$packageName/src") {
#                 $versionFileContent | Set-Content $versionFilePath
#                 Write-Host "✅ Created version.py with version $version at $versionFilePath"
#             } else {
#                 # Fallback to package root if src doesn't exist
#                 $versionFilePath = "$packageName/version.py"
#                 $versionFileContent | Set-Content $versionFilePath
#                 Write-Host "✅ Created version.py with version $version at $versionFilePath"
#             }
#         } else {
#             Write-Host "⚠️  Could not extract version from setup.py"
#         }
#     } else {
#         Write-Host "⚠️  setup.py not found, skipping version.py creation"
#     }
# }

# Create-VersionFile

if ($build) {
    # if(Test-path .\$PackageRepo -PathType leaf) {
    #     Set-Location .\$PackageRepo
    # }

    # If there is not .env file, copy it
    # copy D:\repos\circles\.env .

    # If there is not venv directory, create it
    if(-not(Test-path $venvDirectory -PathType leaf)) {
        Write-Host "Creating venv in $venvDirectory . PYENV_ROOT=$Env:PYENV_ROOT . Running penv init"
        # Done in .bashrc or profile.ps1 so commented
        # $Env:PYENV_ROOT="$HOME/.pyenv"
        pyenv init
        Write-Host "pyenv shell 3.13.3"
        pyenv shell 3.13.3
        python -m venv $venvDirectory
        #& $pythonExecutable -m venv $venvDirectory
    }
    Write-Host "Activating venv directory=" $venvDirectory
    if ( Test-Path -Path $venvDirectory/Scripts/activate ) {
        Write-Host "Running " $venvDirectory/Scripts/activate
        & $venvDirectory/Scripts/activate
    }
    elseif (Test-Path -Path $venvDirectory/bin/Activate.ps1) {
        Write-Host "Running " $venvDirectory/bin/Activate.ps1
        & $venvDirectory/bin/Activate.ps1
    }
    else {
        Write-Host "Error: activate script in venv directory=" $venvDirectory "not found. Exiting script."
        #exit 1
    }

    # Maybe this run after activating the virtual environment (venv)
    Write-Host "Upgrading pip in venv"
    #python.exe -m pip install --upgrade pip
    #& $pythonExecutable -m pip install --upgrade pip
    # TODO python3 with and without --user doesn't work in the powershell script, I tried python3->python
    # Can't use --user after activating the venv
    Write-Host "python -m pip install --upgrade pip"
    python -m pip install --upgrade pip

    # Pause
    # Start-Sleep -Seconds 1.5

    # Write-Host "python3 -m pip install --upgrade pip"
    # python3 -m pip install --upgrade pip

    Write-Host "------------------ Install dependencies ------------------"

    Write-Host "Path when running pip install -r requirements.txt" $PWD.Path

    # Loop until all packages required published
    $counter=0
    do {
        Write-Host "Attempting to install requirements.txt (For the pytest)"
        try {
            pip install -r requirements.txt *> pip_install_output.txt
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Requirements installed successfully."
                $isSuccess = $true
            } else {
                Write-Host "❌ Installation failed (maybe problem with requirements.txt). Retrying..."
                $counter++
                Start-Sleep -Seconds 5
            }
        } catch {
            Write-Host "❌ Error occurred (Maybe one of the packages version you require was not publish yet) ). Retrying..."
            $counter++
            Start-Sleep -Seconds 5
        }
        Write-Host "In loop counter=$counter"
    } until ($isSuccess -or $counter -ge 5)

    if (-not $sucess) {
        Get-Content -Path pip_install_output.txt
    }

    if ($dependencyPackagesToMonitor) {
        Write-Host "grep of dependencyPackagesToMonitor=$dependencyPackagesToMonitor"
        grep $dependencyPackagesToMonitor pip_install_output.txt
        # Pause
    }

    # After successful install, search for monitored packages in the output file
    Write-Host "🔍 Searching for monitored packages in pip_install_output.txt..."
    foreach ($package in $dependencyPackagesToMonitor) {
        $matches2 = Select-String -Path "pip_install_output.txt" -Pattern $package
        if ($matches2) {
            Write-Host "✅ Found '$package' in the output:"
            $matches2 | ForEach-Object { Write-Host $_.Line }
        } else {
            Write-Host "⚠️  Package '$package' not found in the output."
        }
    }
    # Pause

    Write-Host "Foreach dependencyPackagesToMonitor=$dependencyPackagesToMonitor Select-String"
    foreach ($package in $dependencyPackagesToMonitor) {
        Write-Host "`nSearching for '$package'..."
        Select-String -Path "pip_install_output.txt" -Pattern $package
    }
    # Pause

    Write-Host "pip install -r twice TODO"
    # TODO Not working in Ubuntu as primary operating system (not root), so we added "sudo " but it asks for password
    #pip install -r requirements.txt | grep database-infrastructure-local
    if ($dependencyPackagesToMonitor) {
        pip install -r requirements.txt | grep $dependencyPackagesToMonitor
    } else {
        pip install -r requirements.txt
    }
    # Pause
    # Start-Sleep -Seconds 1.5

    #pip show database-infrastructure-local
    if ($dependencyPackagesToMonitor) {
        Write-Host "pip show twice TODO"
        pip show $dependencyPackagesToMonitor
    }
    # Pause

    Remove-Item pip_install_output.txt

    # Pre-Build (Sql2Code)
    if ($currentDirectory-eq "database-mysql-local-python-package") {
        Write-Host "------------------ Pre-build PreTest ------------------"
        pwsh /repos/circlez/sql2code-local-python-package/sqltocode_local_python/bin/generate_table_definition.ps1
    }

    # Remove license line from setup.py classifiers before build
    Write-Host "------------------ Remove License Line ------------------"
    if (Test-Path "setup.py") {
        (Get-Content "setup.py") | Where-Object { $_ -notmatch '"License :: Other/Proprietary License",' } | Set-Content "setup.py"
        Write-Host "Removed license line from setup.py classifiers"
    }

    try { 
        Write-Host "------------------ Build ------------------"
        pip install build
        python -m build
        #& $pythonExecutable -m build
        #python3.12 -m build
    }
    catch
    {
        $ErrorOccurred=$true
        "Error Occurred during pip install build or python -m build. ErrorOccurred=$ErrorOccurred"
    }
}

if ($lint) {
    try { 
        Write-Host "------------------ Lint ------------------"
        # https://flake8.pycqa.org/en/latest/
        #python -m pip install flake8
        & $pythonExecutable -m pip install flake8
        # Make sure those are in sync with publish_python_package.yml
        # TODO We need to uncomment
        Write-Host "Flake8 #1 --select=E9,F63,F7,F82"
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        $LASTEXITCODE1 = $LASTEXITCODE
        Write-Host "Flake8 #1 LASTEXITCODE1=$LASTEXITCODE1"

        Write-Host "Flake8 #2 --exit-zero --max-complexity=10"
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
        $LASTEXITCODE2 = $LASTEXITCODE
        Write-Host "Flake8 #2 LASTEXITCODE2=$LASTEXITCODE2"
        if ($LASTEXITCODE1 -ne 0 -or $LASTEXITCODE2 -ne 0) {
            # This is important so we will not run the GHA FinOps
            Write-Host "Error: Flake8 failed. Exiting script."
            exit -1
        }
    }
    catch
    {
        $ErrorOccurred=$true
        Write-Output "Error Occurred in linter. ErrorOccurred=$ErrorOccurred"
    }
}

if ($test -and -not $ErrorOccurred) {
    try { 
        Write-Host "------------------ $envFilename ------------------"
        if(-not(Test-path $envFilename -PathType leaf)) {
            Write-Host "TODO Can we use env:PERSONAL_REPO=$env:PERSONAL_REPO"
            Write-Host "File $envFilename does not exist, copying from $OrganizationReposDirectory/tal-circlez.ai/circlez.ai/.env.play1"
            $originalEnvFilename = "$OrganizationReposDirectory/tal-circlez.ai/circlez.ai/.env.play1"
            Write-Host "Creating file $envFilename from $originalEnvFilename"
            Copy-Item $originalEnvFilename .
            $lastExitCode = $LASTEXITCODE
            if ($lastExitCode -ne 0) {
                Write-Host "Error: Copying .env.play1 failed. lastExitCode=$lastExitCode. Exiting script."
                exit -1
            }
        }
        else {
            Write-Host "File $envFilename already exists"
        }

        # TODO Have "---" in all Write-Hosts, maybe by using a function
        Write-Host "--- Test using pytest and $envFilename dotenv file and importantTests=$importantTests ---"
        #pytest
        pip install "python-dotenv[cli]"

        Write-Host "pyenv local 3.13.3 before testing"
        # TODO Use environment variable for the python version
        pyenv local 3.13.3
        pip install pytest
        $lastExitCode = 0
        if ($importantTests) {
            Write-Host "dotenvCommand=$dotenvCommand"
            $importantTestsCommand = $dotenvCommand + "python -m pytest " + $importantTests
            # Trying to resolve the + in the begging of $importantTestsCommand
            $importantTestsCommand = "python -m pytest " + $importantTests
            # Write-Host "We found important tests=$importantTests, running dotenv -f $envFilename run pytest $importantTests"
            Write-Host "We found important tests=$importantTests, running:" $importantTestsCommand
            # dotenv -f $envFilename run pytest $importantTests
            Write-Host "Running specific tests first: $importantTests from $PWD"
            # TODO Can we delete this?
            # dotenv -f $envFilename run python -m pytest $importantTests
            # Invoke-Expression do not preserve the $LASTEXITCODE of the command
            # Invoke-Expression "$importantTestsCommand"
            # Not working
            # & "$importantTestsCommand"
            # $lastExitCode = $LASTEXITCODE
            $process = Start-Process -FilePath "python" -ArgumentList "-m pytest $importantTests" -Wait -PassThru
            Write-Output "Pytest #1 (important tests) Exit Code: $($process.ExitCode)"
            $lastExitCode = $($process.ExitCode)
            Write-Host "After running important tests lastExitCode=$lastExitCode"
            if ($lastExitCode -ne 0) {
                Write-Host "Error: pytest failed for important tests. lastExitCode=$lastExitCode. Exiting script. Maybe the important tests in make_py.ps1 are not correct?"
                exit -1
            }
        }
        else {
            Write-Host "No important tests defined for this repository. You can define importantTests in make_py.ps1"
        }

        if ($lastExitCode -eq 0) {  # } -and ($args -contains "all" -or -not $importantTests)) {
            if ($importantTests) {
                Write-Host "Running all tests (Pytest #2) as important tests passed with no critical errors"
            }
            else {
                Write-Host "Running all tests (Pytest #2) as no important tests defined to this repo in make_py.ps1"    
            }
            # TODO use $dotenvCommand
            # dotenv -f $envFilename run python -m pytest
            # We can't get the LASTEXITCODE of the pytest if we use "python -m pytest"
            #$testAllCommand = $dotenvCommand + "python -m pytest"
            $testAllCommand = $dotenvCommand + "pytest"
            Write-Host "Running all tests #1 with command testAllCommand=$testAllCommand"
            Invoke-Expression $testAllCommand
            $lastExitCode = $LASTEXITCODE
            Write-Host "pytest #1 testAllCommand=$testAllCommand lastExitCode=$lastExitCode TODO get the lastExitCode of the dotenv and not the pytest"
            Pause
        }

        if ($lastExitCode -ne 0) {
            Write-Host "Error: pytest failed the full tests. Exiting script."
            exit -1
        }
        Write-Host "pytest #1 passed"
    }
    catch
    {
        Write-Host "Error"
    }
    try {
        # Write-Host "Current Directory PWD.Path=$PWD"
        $pytestCommand = $dotenvCommand + "python -m pytest -x -s --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov-report=xml:coverage.xml --cov=."
        Write-Host "Running pytest #3 with command pytestCommand=$pytestCommand"
        Write
        # & "pytest -x -s --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov-report=xml:coverage.xml --cov=. | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0"
        # TODO Cause /usr/bin/test: missing argument after ‘0’
        # Invoke-Expression "$pytestCommand | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0"
        Invoke-Expression "$pytestCommand | tee pytest-coverage.txt"
        $lastExitCode = $LASTEXITCODE
        Write-Host "pytest #3 lastExitCode=$lastExitCode TODO fix the error above"
        if ($lastExitCode -ne 0) {
            $ErrorOccurred=$true
            Write-Host "Error: pytest failed. lastExitCode=$lastExitCode. ErrorOccurred=$ErrorOccurred. Exiting script."
            exit -1
        }
    }
    catch
    {
        Write-Output "catch Error Occurred in pytest (maybe pytest-cov is not in requirements.txt). Running pytest again to show the error"
        Write-Host "pytestCommand=$pytestCommand"
        # Run the same command with no pipe so we can see the error
        # TODO Use variable with command in both cases
        # & "pytest -x -s --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov-report=xml:coverage.xml --cov=."
        Invoke-Expression $pytestCommand
        Write-Host "Please review the errors while running pytest"
        $lastExitCode = $LASTEXITCODE
        $ErrorOccurred=$true
        Write-Host "pytest #4 again without pipe lastExitCode=$lastExitCode ErrorOccurred=$ErrorOccurred"
    } # catch
}

# TODO As this code is also in make_ts and make_ps, make one function
if ($checkDuplicates -and -not $ErrorOccurred) {
    Write-Host "------------------ Find duplicate files (We don't want to have files with identical names) ------------------"
    # find_duplicate_files .
    if ( $env:GIT_REPOS_DIRECTORY ) {
        Write-Host "Find duplicated in $env:GIT_REPOS_DIRECTORY"
        find_duplicate_files $env:GIT_REPOS_DIRECTORY
    }
    else {
        Wrote-Host "Find duplicates in upper directory repo-directory"
        find_duplicate_files ..
    }
}

# TODO Execute the main?

# Write-Host "------------------ Serverless offline start ------------------"
# TODO Install serverless offline
# serverless offline start

# TODO If it is the same code in TypeScript and Python make one function in sdk-local
if ($run -and -not $ErrorOccurred) {
    $currentDir = Get-Location
    $dirName = $currentDir.Name
    Write-Host "$currentDir="+ $currentDir
    Write-Host "$dirName="+ $dirName

    #if ($dirName -match "serverless-com" -and $dirName -match $word2) {
    if ($currentDir -match "serverless-com") {
        Write-Host "Directory name contains serverless-com"
        $serverlessDotCom=$true
    }

    if ($serverlessDotCom) {
        Write-Host "------------------ Serverless offline start ------------------"

        if (Get-Command serverless -ErrorAction SilentlyContinue) {
            Write-Host "Serverless is installed. Version:"
            serverless --version
        } else {
            Write-Host "Serverless is NOT installed."
            # TODO Make sure it works
            Install-And-Update-Serverless-com
        }

        # TODO How can we run serverless with node --trace-deprecation?
        Write-Host "------------------ Serverless offline start ------------------"
        # TODO We should run serverless in the background so things can continue and ask the user if he tested the server side using client/remote package
        serverless offline start --stage local

        Write-Host "Please update the calling microservice/client/remote package component.json with the port displayed on the screen"
        Read-Host "Have you tested the server with remote package and every things works fine? (y/N)" -OutVariable response
        if ($response -eq "[yY]") {
            $ErrorOccurred = $false
        }
        else {
            $ErrorOccurred = $true
        }
    } # isServerlessDotCom
} # isRun

# We should always cleanup before we push code
if ($cleanup) {
    # TODO Can we run it also if pytest failed
    Write-Host "Delete __pycache__", "dist", "*.egg-info"
    # Get-ChildItem -Path . -Recurse -Directory -Include "__pycache__", "dist", "*.egg-info", ".pytest_cache" | Remove-Item -Recurse -Force
    Get-ChildItem -Recurse -Directory -Path ("__pycache__", "dist", "*.egg-info", ".pytest_cache", "logzio-failures*") | Remove-Item -Recurse -Force
}

# TODO Set $usingLatestVersionsOfPackages to false, if there is == in the requirements.txt. Display a warning if not $usingLatestVersionsOfPackages

# Adding .env* and !.env.*.example to the .gitignore
function Update-GitIgnore {
    # .gitignore should be in the root of the repo so it will impact all the directories
    $gitIgnorePath = "../.gitignore"
    $patterns = @(".env*", "!.env.*.example")
    
    if (Test-Path $gitIgnorePath) {
        $content = Get-Content $gitIgnorePath -Raw
        $modified = $false
        
        foreach ($pattern in $patterns) {
            if ($content -notmatch [regex]::Escape($pattern)) {
                $content += "`n$pattern"
                $modified = $true
            }
        }
        
        if ($modified) {
            $content | Set-Content $gitIgnorePath
            Write-Host "Added patterns to .gitignore"
        } else {
            Write-Host "Patterns already exist in .gitignore"
        }
    } else {
        $patterns -join "`n" | Set-Content $gitIgnorePath
        Write-Host "Created .gitignore with required patterns"
    }
}

# Write-Host "Directory before calling to Update-GitIgnore PWD.Path=" $PWD.Path
if (-not $ErrorOccurred) {
    Update-GitIgnore
    # Pause    
}


# git push and create Draft PR
# TODO Make sure we run CreateDraftPr.ps1 (from sdk-local) only if things are ok
Write-Host "Before git push and create Draft PR $ErrorOccurred=$ErrorOccurred"
if (-not $ErrorOccurred) {
    # Call GitPull.ps1 with appropriate switches based on flags
    if ($isPush -and $isCreateDraftPr) {
        & CreateDraftPr.ps1 -All
    }
    elseif ($isPush) {
        & CreateDraftPr.ps1 -Push
    }
    elseif ($isCreateDraftPr) {
        & CreateDraftPr.ps1 -CreateDraftPr
    }
} else {
    Write-Host "We do not push and create Draft PR, since there were errors"
    exit -1
}

# TODO if not $usingLatestVersionsOfPackages, change == to >= in requirements.txt and re-run the make_py.ps1 again
