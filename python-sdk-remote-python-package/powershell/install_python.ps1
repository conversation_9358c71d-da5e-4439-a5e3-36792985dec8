# TODO Doesn't work in Ubuntu PowerShell. We should check it if it is working in Windows PowerShell.

# Define the version you want
$pythonVersion = "3.12.3"
$pythonExe = "python.exe"

# Function to check Python version
function Get-PythonVersion {
    try {
        $output = & $pythonExe --version 2>&1
        if ($output -match "Python (\d+\.\d+\.\d+)") {
            return $Matches[1]
        } else {
            return $null
        }
    } catch {
        return $null
    }
}

# Check current Python version
$currentVersion = Get-PythonVersion

if ($currentVersion -eq $pythonVersion) {
    Write-Host "Python $pythonVersion is already installed."
} else {
    Write-Host "Python $pythonVersion not found. Installing..."

    # Download Python installer
    $installerUrl = "https://www.python.org/ftp/python/$pythonVersion/python-$pythonVersion-amd64.exe"
    $installerPath = "$env:TEMP\python-$pythonVersion-installer.exe"

    Write-Host "Downloading Python $pythonVersion installer..."
    Invoke-WebRequest -Uri $installerUrl -OutFile $installerPath

    # TODO I think we should activate the venv here

    Write-Host "Installing Python $pythonVersion..."
    & $installerPath /quiet InstallAllUsers=1 PrependPath=1 Include_test=0

    # Cleanup
    Remove-Item $installerPath

    # Verify installation
    $newVersion = Get-PythonVersion
    if ($newVersion -eq $pythonVersion) {
        Write-Host "Python $pythonVersion installed successfully."
    } else {
        Write-Host "Failed to install Python $pythonVersion."
    }
}
