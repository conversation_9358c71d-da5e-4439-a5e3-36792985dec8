rem D:\repos\circles\python-sdk-remote-python-package\python-sdk-remote-python-package\bin\cleanup.bat

d:
echo 1
pushd \repos\circles

Rem Python
FOR /d /r . %%d IN (.pytest_cache) DO @IF EXIST "%%d" rd /s /q "%%d"
FOR /d /r . %%d IN (*.egg-info) DO @IF EXIST "%%d" rd /s /q "%%d"
FOR /d /r . %%d IN (venv) DO @IF EXIST "%%d" rd /s /q "%%d"
FOR /d /r . %%d IN (.venv) DO @IF EXIST "%%d" rd /s /q "%%d"

Rem TypeScript and Serverless (Both TypeScript and Python)
FOR /d /r . %%d IN (node_modules) DO @IF EXIST "%%d" rd /s /q "%%d"
FOR /d /r . %%d IN (package-local.json) DO @IF EXIST "%%d" rd /s /q "%%d"
FOR /d /r . %%d IN (coverage) DO @IF EXIST "%%d" rd /s /q "%%d"
FOR /d /r . %%d IN (dist) DO @IF EXIST "%%d" rd /s /q "%%d"

popd