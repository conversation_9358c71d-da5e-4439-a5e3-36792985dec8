function create-venv {
    python -m venv .venv
}

function activate-venv {
    .\.venv\Scripts\Activate.ps1
}

function upgrade-pip {
    python -m pip install --upgrade pip
}

function install-req {
    pip install --upgrade -r requirements.txt
}

function update-req {
    # Read the requirements file
    $requirements = Get-Content -Path requirements.txt

    # Create an empty array to hold the package names
    $packageNames = @()

    # Loop over each line in the requirements file
    foreach ($line in $requirements) {
        # If the line is a package requirement
        if ($line -match '^[a-zA-Z0-9]') {
            # Get the package name (up to the first space or equals sign)
            $packageName = ($line -split '[>= #]')[0]

            # Add the package name to the array
            $packageNames += $packageName
        }
    }

    # Install the latest versions of the packages
    foreach ($packageName in $packageNames) {
        pip install --upgrade --disable-pip-version-check $packageName
    }

    # Create an empty array to hold the updated requirements
    $updatedRequirements = @()

    # Loop over each line in the requirements file
    foreach ($line in $requirements) {
        # If the line is a package requirement
        if ($line -match '^[a-zA-Z0-9]') {
            # Get the package name (up to the first space or equals sign)
            $packageName = ($line -split '[>= #]')[0]

            # Find the installed version of this package
            $installedVersion = (pip show $packageName | Where-Object { $_ -match "^Version:" }).Split(' ')[1]

            # If an installed version was found, replace the line with it
            if ($installedVersion) {
                $line = "$packageName>=$installedVersion    # https://pypi.org/project/$packageName/"
            }
        }

        # Add the line to the updated requirements
        $updatedRequirements += $line
    }

    # Write the updated requirements back to the file
    $updatedRequirements | Out-File -FilePath requirements.txt
}

function update-req-exact {
    # Read the requirements file
    $requirements = Get-Content -Path requirements.txt

    # Create an empty array to hold the package names
    $packageNames = @()

    # Loop over each line in the requirements file
    foreach ($line in $requirements) {
        # If the line is a package requirement
        if ($line -match '^[a-zA-Z0-9]') {
            # Get the package name (up to the first space or equals sign)
            $packageName = ($line -split '[>= #]')[0]

            # Add the package name to the array
            $packageNames += $packageName
        }
    }

    # Install the latest versions of the packages
    foreach ($packageName in $packageNames) {
        pip install --upgrade --disable-pip-version-check $packageName
    }

    # Get the list of installed packages and their versions
    $installedPackages = pip list --format=freeze

    # Create an empty array to hold the updated requirements
    $updatedRequirements = @()

    # Loop over each line in the requirements file
    foreach ($line in $requirements) {
        # If the line is a package requirement
        if ($line -match '^[a-zA-Z0-9]') {
            # Get the package name (up to the first space or equals sign)
            $packageName = ($line -split '[>= #]')[0]

            # Find the installed version of this package
            $installedVersion = $installedPackages -match "^$packageName==" | Out-String

            # If an installed version was found, replace the line with it
            if ($installedVersion) {
                $installedVersion = $installedVersion.Trim()
                $line = $line -replace "$packageName[>=]*\d.*", $installedVersion
            }

            # If the line does not contain a PyPI link, add it
            if ($line -notmatch "https://pypi.org/project/") {
                $line += "    # https://pypi.org/project/$packageName/"
            }
        }

        # Add the line to the updated requirements
        $updatedRequirements += $line
    }

    # Write the updated requirements back to the file
    $updatedRequirements | Out-File -FilePath requirements.txt
}

function uninstall-req {
    # Read the requirements file
    $requirements = Get-Content -Path requirements.txt

    # Create an empty array to hold the package names
    $packageNames = @()

    # Loop over each line in the requirements file
    foreach ($line in $requirements) {
        # If the line is a package requirement
        if ($line -match '^[a-zA-Z0-9]') {
            # Get the package name (up to the first space or equals sign)
            $packageName = ($line -split '[ =#]')[0]

            # Add the package name to the array
            $packageNames += $packageName
        }
    }

    # Uninstall each package
    foreach ($packageName in $packageNames) {
        pip uninstall -y $packageName
    }
}

function update-setup-from-req {
    # Read the requirements file
    $requirements = Get-Content -Path requirements.txt

    # Read the setup.py file
    $setup = Get-Content -Path setup.py

    # Create an empty array to hold the updated install_requires list
    $updatedInstallRequires = @()

    # Loop over each line in the requirements file
    foreach ($line in $requirements) {
        # If the line is a package requirement
        if ($line -match '^[a-zA-Z0-9]') {
            # Remove the comment from the line
            $line = ($line -split '#')[0].Trim()

            # Add the line to the updated install_requires list, formatted as a Python string
            $updatedInstallRequires += "`t`t'$line',"
        }
    }

    # Find the index of the install_requires line in the setup.py file
    $installRequiresIndex = ($setup | Select-String -Pattern 'install_requires=\[' -List | Select-Object -First 1).LineNumber

    # Find the index of the closing bracket for the install_requires list
    $closingBracketIndex = ($setup[$installRequiresIndex..$setup.Length] | Select-String -Pattern '\]' -List | Select-Object -First 1).LineNumber + $installRequiresIndex - 1

    # Check that the indices are within the valid range for the array
    if ($installRequiresIndex -gt 2 -and $closingBracketIndex -gt 0 -and $closingBracketIndex -lt $setup.Length) {
        # Remove the existing install_requires list from the setup.py file
        $setup = $setup[0..($installRequiresIndex-2)] + $setup[($closingBracketIndex+1)..$setup.Length]

        # Insert the updated install_requires list into the setup.py file
        $setup = $setup[0..($installRequiresIndex-2)] + "`tinstall_requires=[" + $updatedInstallRequires + "`t]," + $setup[($installRequiresIndex-1)..$setup.Length]
    }

    # Write the updated setup.py file back to disk
    $setup | Out-File -FilePath setup.py
}
