-- Purpose: This report presents all the imports done do the system group by each source

-- TODO: we need to change it to present them only from a certain time as we might have a lot of data in the resultset

-- If you need to add a parameter
-- https://stackoverflow.com/questions/2281890/can-i-create-view-with-parameter-in-mysql

-- Must have USE statement where the view should be created
USE entity_type;

-- The Name of the view should be meaningful and according to our standard naming convention
-- DROP VIEW entity_types_imported_group_by_source_view;
CREATE OR REPLACE VIEW entity_types_imported_group_by_source_view AS
SELECT entity_type.entity_type_ml_table.title AS 'Entity Type Name', data_source_name AS 'Data Source Name', count(*) as 'Number of Entries'
FROM importer.importer_table
         JOIN entity_type.entity_type_ml_table
              ON entity_type.entity_type_ml_table.entity_type_id = importer.importer_table.entity_type_id
         JOIN data_source.data_source_ml_table ON data_source.data_source_ml_table.data_source_id = importer.importer_table.data_source_id
GROUP BY data_source.data_source_ml_table.data_source_name;

SELECT *
FROM entity_types_imported_group_by_source_view
LIMIT 5;

SELECT *
FROM `entity_type_ml_en_view`
LIMIT 5;

