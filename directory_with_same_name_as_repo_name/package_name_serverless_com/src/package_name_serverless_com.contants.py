from logger_local.LoggerComponentEnum import LoggerComponentEnum

WHATSAPP_MESSAGE_INFORU_API_TYPE_ID = 8

WHATSAPP_LOGGER_COMPONENT_ID = 298
WHATSAPP_LOGGER_COMPONENT_NAME = "WhatsApp_InforU_SERVERLESS_PYTHON"
WHATSAPP_DEVELOPER_EMAIL = "<EMAIL>"

# TODO Shall we use ComponentId or component_id? as we have similar code also in TypeScript and we send it in JSON.
def get_logger_object(category: str = LoggerComponentEnum.ComponentCategory.Code):
    if category == LoggerComponentEnum.ComponentCategory.Code:
        return {
            'component_id': WHATSAPP_LOGGER_COMPONENT_ID,
            'component_name': WHATSAPP_LOGGER_COMPONENT_NAME,
            'component_category': LoggerComponentEnum.ComponentCategory.Code,
            'developer_email_address': WHATSAPP_DEVELOPER_EMAIL
        }
    elif category == LoggerComponentEnum.ComponentCategory.Unit_Test:
        return {
            'component_id': WHATSAPP_LOGGER_COMPONENT_ID,
            'component_name': WHATSAPP_LOGGER_COMPONENT_NAME,
            'component_category': LoggerComponentEnum.ComponentCategory.Unit_Test,
            'developer_email_address': WHATSAPP_DEVELOPER_EMAIL
        }