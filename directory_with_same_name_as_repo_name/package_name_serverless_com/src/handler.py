import json	
from lambda_decorators import cors_headers

# TODO Improve secutity when relevant using https://www.serverless.com/framework/docs/providers/aws/events/apigateway/#http-endpoints-with-custom-authorizers

@cors_headers
# TODO add _handler suffix
def example_lambda(event, context):
    # TODO In all serverless-com handlers' logger.start()- Add optional parameter to logger.start() which called api_call, so logger.start will call api_management to insert into api_call_table all fields including session_id.
    # TODO User the functions in python-sdk to create the body
    body = {
        "message": "Go Serverless v1.0! Your function executed successfully!",
        "input": event
    }

    # TODO User the functions in python-sdk to create the response
    # We should replace 200 with HttpResponse also in the template https://github.com/circles-zone/python-serverless-com-template/edit/dev/directory_with_same_name_as_repo_name/handler.py
    response = {
        "statusCode": 200,
        "body": json.dumps(body)
    }
    # TODO logger.end()
    return response
