# Please make sure we are using the latest version
# Used by "pip install -r requirements.txt" in GitHub Actions yml file

# TODO Please delete all the lines which are not inuse

# Not Circlez.ai requirments (sorted by alphabet)
lambda-decorators
#mysql-connector>=2.2.9 # https://pypi.org/project/mysql-connector
#mysql-connector-python>=8.0.33 #https://pypi.org/project/mysql-connector-python
#pymysql>=1.1.0 # PyMySQL https://pypi.org/project/pymysql
python-dotenv>=1.0.0 # https://pypi.org/project/python-dotenv

# Circlez.ai requirments (sorted by alphabet)
database-local>=0.0.6 # database-without-orm https://pypi.org/project/database-local/
#importer-local>=0.0.6 # importer when importing data from external source to our system https://pypi.org/project/importer-local/
logger-local>=0.0.11 # logger (writes to console, database, Logz.io ...) https://pypi.org/project/logger-local/
#storage-local>=0.0.5 # storage store images, files, pictures, documents https://pypi.org/project/storage-local/

# Only for testing
# pytest>=7.4.0 # https://pypi.org/project/pytest/
pytest # https://pypi.org/project/pytest/