# TODO: Preferable the same name as the name of the repo, but it is too long

# TODO We need to make a decision https://stackoverflow.com/questions/46857335/how-to-remove-stage-from-urls-for-aws-lambda-functions-serverless-framework

# TODO Can we move this file into the repo-directory?

# For example:
#   service: 'group-restapi-py-slc'
#   service: 'group-graphql-py-slc'
# Can't be too long (overall length should be 63 bytes including suffix)
#        xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx 
service: 'XXX-restapi-py-slc'
app: Circlez
org: Circlez.ai

# Should be correlated to package.json version

# TODO We are trying frameworkVersion 4 in logger-server-restapi, google-account-restapi-python (works). we need to update both the python and typescript template
frameworkVersion: '^3.38.0' # https://github.com/serverless/serverless/releases 3.35.2 -> 3.38.0

# In Serverless V3 we need it, in Serverless V4 we don't need it
useDotenv: true

provider:
  name: aws
  # TODO Can we use newer Python version?
  runtime: python3.11 # Latest is python3.11, As of now not supporting python3.12 . It should be the same Python version as we have in the GHA YML
  # Those two should be commented, this way we can support multi-Environments?
  #stage: play1
  # This causes serverless.com to add the stage / environment_name in the beginning of the endpoint url
  # TODO Shall we use provider.stage or environment.STAGE?
  # stage: '${opt:stage, "play1"}'
  # This is how we use it in google-account-restapi-python-serverless-com repo - We prefer to use ENVIRONMENT_NAME
  # In python serverless.yml provider: stage: is mandatory so the endpoint URL/URL will include the environment name and not /dev
  # TODO Shall we use opt: or env:?
  stage: "${env:ENVIRONMENT_NAME}"
  #region: us-east-1

  # TODO All environment variables should be synced with GitHub Actions (GHA) Workflow test job environment variables 
  environment:
    # TODO Do we need it? What is the impact of STAGE? - Maybe we don't need it if we have provider.stage
    # TODO Should we use env:ENVIRONMENT_NAME?
    # STAGE: "${env:STAGE}"

    BRAND_NAME: "${env:BRAND_NAME}"
    ENVIRONMENT_NAME: "${env:ENVIRONMENT_NAME}"

    JWT_SECRET_KEY: "${env:JWT_SECRET_KEY}"
    USER_JWT_SECRET_KEY: "${env:USER_JWT_SECRET_KEY}"

    LOGZIO_TOKEN: "${env:LOGZIO_TOKEN}"
    LOGGER_MINIMUM_SEVERITY: "${env:LOGGER_MINIMUM_SEVERITY, 'INFO'}"
    LOGGER_CONFIGURATION_JSON_PATH: "${env:LOGGER_CONFIGURATION_JSON_PATH, ''}"

    RDS_HOSTNAME: "${env:RDS_HOSTNAME}"
    RDS_USERNAME: "${env:RDS_USERNAME}"
    RDS_PASSWORD: "${env:RDS_PASSWORD}"

# TODO Please make sure it is synced with package.json
plugins:
  # TODO Should run "serverless plugin install -n serverless-python-requirements" in the repo-directory where we have the package.json file
  # Must have serverless-python-requirements plugin in serverless.yml as we install it in talr/actions.serverless-with-python-requirements@master
  - serverless-python-requirements # Is it really mandatory
  - serverless-offline
  #- serverless-secrets-plugin

package:
  patterns:
    - "!node_modules/**"
    - "!package-lock.json"
    - "!package.json"

functions:
  send_message:
    handler: handler.send_message
    timeout: 30
    #    The following are a few example events you can configure
    #    NOTE: Please make sure to change your handler code to work with those events
    #    Check the event documentation for details
    events:
      # HTTP API (API Gateway v2) https://www.serverless.com/framework/docs/providers/aws/events/http-api
      - httpApi:
          path: /${self:provider.environment.ENVIRONMENT_NAME}/api/v1/dialogWorkflow/sendMessage
          method: post
          cors: true

