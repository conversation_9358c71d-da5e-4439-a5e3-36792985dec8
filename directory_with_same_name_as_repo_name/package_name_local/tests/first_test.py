import pytest
import unittest
import sys
import os
from unittest.mock import patch, MagicMock
# The following 4 lines prevents the error "ImportError: attempted relative import with no known parent package"
from dotenv import load_dotenv
load_dotenv()
script_directory = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(script_directory, '..'))


def test_func_name():
    assert True


# The following 2 lines are requires to run pytest with the vs code run button
if __name__ == "__main__":
    pytest.main(sys.argv[1:])