-- Other tables api_call

SET @START_SEVERITY_ID := 400;
SET @END_SEVERITY_ID := 402;

-- 3 records, probably by one of the tests
SELECT *
-- DELETE
FROM logger.logger_table
WHERE field_value_old = "test_old"
   OR field_value_new = "test_new";

-- 0 records
SELECT COUNT(*)
-- DELETE
FROM logger.logger_table
WHERE created_timestamp < "2024-01-01"
  AND variable_id IS NULL
;

-- Deleted 171 records
SELECT COUNT(*)
-- DELETE
FROM logger.logger_table
WHERE is_test_data IS TRUE
;

-- 0 records
-- If the SELECT results looks good, uncomment the DELETE
SELECT *
-- DELETE
FROM logger.logger_table
WHERE variable_id IS NULL

  AND severity_id < 500
  AND severity_id <> @START_SEVERITY_ID
  AND severity_id <> @END_SEVERITY_ID-- This is problematic if we user severity for start/end
  AND component_id IS NOT NULL       -- TODO: this is always true
  AND user_id IS NULL
  AND activity_id IS NULL
  AND action_id IS NULL
  AND api_type_id IS NULL
  AND client_ip_v4 IS NULL
  AND client_ip_v6 IS NULL
  AND server_ip_v4 IS NULL
  AND server_ip_v6 IS NULL
-- AND location_id IS NULL
  AND component_id IS NULL           -- TODO: this is always false
  AND user_id is NULL
  AND profile_id IS NULL
  AND (status_id IS NULL OR state_id = 0)
  AND group_id IS NULL
  AND profile_id IS NULL
-- AND client_ip_v4 IS NULL
  AND relationship_type_id IS NULL
  AND state_id IS NULL               -- TODO: this is always false
  AND variable_id IS NULL
  AND (field_id IS NULL OR field_id = 38)
  AND (field_value_old IS NULL OR field_value_old = "test_old")
  AND (field_value_new IS NULL OR field_value_new = "test_new")
-- AND created_timestamp < "2024-01-01"
;

SELECT COUNT(*)
FROM logger.logger_table;

SELECT *
FROM logger.logger_table
ORDER by logger_id DESC
LIMIT 50;

-- We should create this view
SELECT *
FROM logger.logger_general_view
ORDER by logger_id DESC
LIMIT 50;
