-- Overcome old data in user_id and profile_id and migrate to the new fields
-- Do we use logger.user_id and profile_id?
SELECT logger_id,
       logger.created_timestamp,
       logger.user_id,
       logger.profile_id,
       logger.created_real_user_id,
       logger.created_effective_user_id,
       logger.created_effective_profile_id
FROM logger.logger_table logger
         LEFT JOIN user.user_table user_created_real ON user_created_real.user_id = logger.created_real_user_id
WHERE logger.user_id IS NOT NULL
ORDER BY logger.created_timestamp DESC
;

UPDATE logger.logger_table logger
SET created_user_id=user_id,
    created_real_user_id= user_id,
    created_effective_user_id=user_id,
    created_effective_profile_id=profile_id,
    updated_user_id=user_id,
    updated_real_user_id= user_id,
    updated_effective_user_id=user_id,
    updated_effective_profile_id=profile_id,
    user_id = NULL
WHERE logger.user_id IS NOT NULL
-- AND logger_id=88618
;

SELECT *
FROM logger.logger_table
WHERE logger_id = 88618;

-- TODO Now we should delte user_id and profile_id from logger_table
