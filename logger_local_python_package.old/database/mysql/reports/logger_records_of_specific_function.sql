-- Loggger Trace Specific funciton
SELECT logger_id
     , message
     , profile_id
     , location_id
     , record
     , component_id
     , component_name
     , function_name
     , error_stack
     , logger.severity_id
     , severity.name
     , is_test_data
FROM logger.logger_table logger
         LEFT JOIN logger.severity_table severity ON severity.severity_id = logger.severity_id
-- WHERE function_name LIKE "%test_exception%";
WHERE function_name = "test_exception";
