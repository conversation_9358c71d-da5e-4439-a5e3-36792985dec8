SELECT
-- *
    logger.session
     , logger.timestamp
     , logger.created_timestamp              AS logger_timestamp
     , logger.logger_id
     , logger.user_id
     , logger.profile_id
     , logger.component_category
     , logger.record
     , logger.component_name
     , logger.path
     , logger.filename
     , logger.function_name
     , logger.line_number
     , logger.result
     , logger.error_stack
     , logger.computer_language
     , logger.developer_email
     , logger.created_user_id
     , logger.created_real_user_id
     , (SELECT logger_end.created_timestamp
        FROM logger.logger_table logger_end
        WHERE logger_end.session = logger.session
          AND logger_end.created_timestamp > logger.created_timestamp
        LIMIT 1)                             AS logger_end_timestamp
-- Not working
-- , TIMEDIFF( logger.created_timestamp, logger_end_timestamp)
     , logger_in_same_session.component_name AS component_name_before
     , logger_in_same_session.function_name  AS function_name_before
FROM logger.logger_table logger
         LEFT JOIN logger.logger_table logger_in_same_session
                   ON logger_in_same_session.session = logger.session AND logger_in_same_session.session IS NOT NULL
WHERE (logger.severity_id = 900 -- Exception
-- To get old data
    OR logger.severity_id > 700) -- Error
  AND logger.component_category <> "Unit-Test"
  AND logger.created_timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
-- AND component_id = 168
-- AND class_name = "ReactionLocal"
-- AND function_name="insert"
ORDER BY session, logger.created_timestamp DESC;

-- Show diff between the first and last timestamp for a given session
SELECT TIMEDIFF(MAX(created_timestamp), MIN(created_timestamp)) AS session_duration
FROM logger.logger_table
WHERE session = "K6SVGG4ASGCATVKOX00K73PF637PU8";

-- Show diff between the first and last timestamp for each path.
SELECT path,
       session,
       MIN(created_timestamp)                                   AS created_timestamp,
       TIMEDIFF(MAX(created_timestamp), MIN(created_timestamp)) AS session_duration
FROM logger.logger_table
WHERE session IS NOT NULL
  AND path LIKE '/%'
GROUP BY path;

