SELECT
-- *
    logger_id
     , user_id
     , profile_id
     , component_category
     , session
     , record
     , timestamp
     , component_name
     , path
     , filename
     , function_name
     , line_number
     , result
     , computer_language
     , developer_email
     , created_user_id
     , created_real_user_id
     , created_timestamp AS logger_start_timestamp
     , (SELECT logger_end.created_timestamp
        FROM logger.logger_table logger_end
        WHERE component_id = 168
          AND logger_end.created_timestamp > logger_start.created_timestamp
        LIMIT 1)         AS logger_end_timestamp
-- Not working
-- , TIMEDIFF( logger_start.created_timestamp, logger_end_timestamp)
FROM logger.logger_table logger_start
WHERE severity_id in (400, 402)
  AND component_id = 168
-- AND class_name = "ReactionLocal"
  AND function_name = "insert"
ORDER BY timestamp DESC
