SELECT path,
       session,
       MIN(created_timestamp)                                   AS created_timestamp,
       TIM<PERSON><PERSON><PERSON>(MAX(created_timestamp), MI<PERSON>(created_timestamp)) AS session_duration
FROM logger.logger_table
WHERE session IS NOT NULL
  AND path LIKE '/%'
GROUP BY path, session;


SELECT "a"
-- <PERSON><PERSON>(created_timestamp) AS created_timestamp
-- , filename, class_name, function_name, component_type, real_name, user_identifier, developer_email, created_real_user_id, created_effective_user_id, `path`, `session`, TIMEDIFF(MAX(created_timestamp), MIN(created_timestamp)) AS session_duration
FROM logger.logger_table
-- WHERE `session` IS NOT NULL
-- AND path LIKE '/%'
-- AND `path` LIKE '%serverless%'
-- GROUP BY `path`, `session`
-- LIMIT 10
