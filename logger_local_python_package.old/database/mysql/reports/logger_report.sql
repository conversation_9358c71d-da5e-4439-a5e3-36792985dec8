SELECT component_type, computer_language, component_category, testing_framework, component_name, COUNT(*)
  FROM logger.logger_table
  GROUP BY component_type, computer_language, component_category, testing_framework, component_name
  ORDER BY component_type, computer_language, component_category, testing_framework, component_name;

SELECT *
  FROM logger.logger_general_recent_view;

SELECT severity_id, message, created_timestamp, timestamp
  FROM logger.logger_view 
  -- WHERE message = "a" 
  ORDER BY logger_id DESC LIMIT 1;
