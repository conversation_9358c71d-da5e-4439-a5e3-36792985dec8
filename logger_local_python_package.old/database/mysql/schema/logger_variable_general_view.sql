CREATE 
    ALGORITHM = UNDEFINED 
    DEFINER = `bubbelz`@`%` 
    SQL SECURITY DEFINER
VIEW `logger`.`logger_variable_general_view` AS
    SELECT 
        `logger`.`timestamp` AS `timestamp`,
        `logger`.`profile_id` AS `profile_id`,
        `profile`.`profile.main_email_address` AS `profile.main_email_address`,
        `logger`.`state_id` AS `workflow_state_id`,
        `logger`.`variable_id` AS `variable_id`,
        `variable`.`name` AS `variable name`,
        `logger`.`variable_value_old` AS `variable_value_old`,
        `logger`.`variable_value_new` AS `variable_value_new`
    FROM
        ((`logger`.`logger_table` `logger`
        LEFT JOIN `profile`.`profile_table` `profile` ON ((`profile`.`profile_id` = `logger`.`profile_id`)))
        LEFT JOIN `field`.`variable_table` `variable` ON ((`variable`.`variable_id` = `logger`.`variable_id`)))
    WHERE
        (`logger`.`variable_id` IS NOT NULL)
    ORDER BY `logger`.`logger_id` DESC
