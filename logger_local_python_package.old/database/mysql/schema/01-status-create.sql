CREATE SCHEMA IF NOT EXISTS logger;
USE logger;
CREATE TABLE IF NOT EXISTS status_table
(

    id                TINYINT UNSIGNED UNIQUE AUTO_INCREMENT COMMENT "PK: status_id",

    created_timestamp TIMESTAMP DEFAULT NOW()             NOT NULL,
    created_user_id   BIGINT UNSIGNED                     NOT NULL,
    updated_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_user_id   BIGINT UNSIGNED                     NOT NULL,
    start_datetime    TIMESTAMP DEFAULT NOW()             NOT NULL,
    end_datetime      TIMESTAMP DEFAULT NULL,

    PRIMARY KEY (id),
    FOREIGN KEY (created_user_id) REFERENCES user.user_table (user_id),
    FOREIG<PERSON> KEY (updated_user_id) REFERENCES user.user_table (user_id)
);

CREATE TABLE IF NOT EXISTS status_table_ml
(

    id          INT UNSIGNED AUTO_INCREMENT COMMENT "PK:status_ml_id",
    status_id   TINYINT UNSIGNED NOT NULL,

    lang_code   CHAR(5)          NOT NULL,
    description VARCHAR(250)     NULL,

    PRIMARY KEY (id),
    FOREIGN KEY (status_id) REFERENCES logger.status_table (id)
);