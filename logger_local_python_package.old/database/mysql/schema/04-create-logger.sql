CREATE SCHEMA IF NOT EXISTS logger;
USE logger;
CREATE TABLE `logger_table` (
  `logger_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK: logger_id',
  `number` bigint unsigned DEFAULT NULL COMMENT 'I don''t think we need it',
  `identifier` varchar(255) DEFAULT NULL COMMENT 'Maybe we need it if we want to share log records from prod with a team member/developer\\nCreating a record in identifier_table for each log record, will slow down writing to the logger_table and impact performance.',
  `client_ip_v4` varchar(15) DEFAULT NULL,
  `client_ip_v6` varchar(45) DEFAULT NULL,
  `server_ip_v4` char(15) DEFAULT NULL,
  `server_ip_v6` char(45) DEFAULT NULL,
  `location_id` bigint unsigned DEFAULT NULL,
  `user_id` bigint unsigned DEFAULT NULL COMMENT 'TODO Shall we delete this column as duplicate?',
  `profile_id` bigint unsigned DEFAULT NULL COMMENT 'TODO Shall we delete this column as duplicate?',
  `activity` varchar(255) DEFAULT NULL,
  `activity_id` int unsigned DEFAULT NULL,
  `action_id` smallint unsigned DEFAULT NULL,
  `message` varchar(2500) NOT NULL,
  `record` varchar(2500) NOT NULL,
  `payload` text,
  `component_id` int unsigned NOT NULL,
  `component_name` varchar(255) DEFAULT NULL,
  `path` varchar(255) NOT NULL,
  `filename` varchar(255) DEFAULT NULL,
  `class_name` varchar(255) DEFAULT NULL,
  `function_name` varchar(255) DEFAULT NULL,
  `line_number` int unsigned DEFAULT NULL,
  `error_stack` text,
  `severity_id` smallint unsigned DEFAULT NULL,
  `status_id` tinyint unsigned DEFAULT NULL,
  `group_id` bigint DEFAULT NULL,
  `relationship_type_id` int DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `state_id` int unsigned DEFAULT '0' COMMENT 'TODO change to dialog_workflow_state_id',
  `variable_id` int unsigned DEFAULT NULL,
  `variable_value_old` text COMMENT 'Shall we use variable_value_old and new also for fields or just for variables?',
  `variable_value_new` text COMMENT 'Shall we use variable_value_old and new also for fields or just for variables?',
  `field_id` smallint unsigned DEFAULT NULL,
  `field_value_old` text COMMENT 'Shall we use variable_value_old and new also for fields or just for variables?',
  `field_value_new` text COMMENT 'Shall we use variable_value_old and new also for fields or just for variables?',
  `session` char(32) DEFAULT NULL COMMENT 'uuid12 VARCHAR(36)\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\nuuid / session_id VARCHAR(20)\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\nShall we rename it to session_token?',
  `thread_id` int DEFAULT NULL,
  `process_id` int DEFAULT NULL,
  `api_type` varchar(45) DEFAULT NULL COMMENT 'REST-API/GraphQL\\ncomponent_table',
  `api_type_id` int unsigned DEFAULT NULL,
  `group_id1` int unsigned DEFAULT NULL,
  `group_id2` int unsigned DEFAULT NULL,
  `component_category` varchar(45) DEFAULT NULL COMMENT 'Code/Unit-Test/E2E-Test\ncomponent_table',
  `testing_framework` varchar(45) DEFAULT NULL COMMENT 'Vitetest/Playwrite/Python Unittest/pytest\ncomponent_table',
  `component_type` varchar(45) DEFAULT NULL COMMENT 'Service/API/Remote\ncomponent_table',
  `computer_language` varchar(45) DEFAULT NULL COMMENT 'Python, TypeScript\ncomponent_table',
  `developer_email` varchar(55) DEFAULT NULL,
  `sql_statement` varchar(255) DEFAULT NULL,
  `sql_parameters` varchar(255) DEFAULT NULL,
  `sql_formatted` varchar(255) DEFAULT NULL,
  `smartlink_identifier` varchar(255) DEFAULT NULL,
  `stdout` text COMMENT 'Used by SmartLink',
  `stderr` text COMMENT 'Used by SmartLink',
  `return_code` int DEFAULT NULL COMMENT 'Used by SmartLink',
  `returned_value` text COMMENT 'Used by SmartLink',
  `return_message` varchar(255) DEFAULT NULL COMMENT 'Used by SmartLink',
  `recipient` text,
  `source_email` varchar(255) DEFAULT NULL,
  `destination_emails` varchar(255) DEFAULT NULL,
  `message_id` bigint unsigned DEFAULT NULL,
  `message_template_id` int unsigned DEFAULT NULL,
  `criteria_id` bigint unsigned DEFAULT NULL,
  `form_id` int unsigned DEFAULT NULL,
  `question_id` int unsigned DEFAULT NULL,
  `message_template_text_block_id` int unsigned DEFAULT NULL,
  `compound_message` text,
  `text_block_id` bigint unsigned DEFAULT NULL,
  `reaction_id` bigint unsigned DEFAULT NULL,
  `result` text,
  `real_name` varchar(255) DEFAULT NULL,
  `user_identifier` varchar(255) DEFAULT NULL,
  `locals_before_exception` json DEFAULT NULL,
  `is_assertion_error` tinyint DEFAULT NULL,
  `smartlink_id` bigint unsigned DEFAULT NULL,
  `json_version` varchar(45) DEFAULT NULL,
  `package_json` varchar(45) DEFAULT NULL,
  `is_test_data` tinyint DEFAULT NULL COMMENT 'Log records from Tests',
  `start_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_timestamp` timestamp NULL DEFAULT NULL,
  `start_datetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Do we need it?',
  `end_datetime` timestamp NULL DEFAULT NULL,
  `created_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_user_id` bigint unsigned DEFAULT NULL COMMENT 'TODO Shall we delete this column as duplicate?',
  `created_real_user_id` bigint unsigned NOT NULL,
  `created_effective_user_id` bigint unsigned NOT NULL,
  `created_effective_profile_id` bigint unsigned NOT NULL,
  `updated_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_user_id` bigint unsigned DEFAULT NULL COMMENT 'TODO Shall we delete this column as duplicate?',
  `updated_real_user_id` bigint unsigned NOT NULL,
  `updated_effective_user_id` bigint unsigned NOT NULL,
  `updated_effective_profile_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`logger_id`,`component_id`,`path`),
  UNIQUE KEY `id` (`logger_id`),
  KEY `logger_table.state_id.fk` (`state_id`),
  KEY `status_id` (`status_id`),
  KEY `logger_table.component_id.fk` (`component_id`),
  KEY `field_id_idx` (`field_id`),
  KEY `logger_table.variable_id.fk` (`variable_id`),
  KEY `logger_table.action_id.fk_idx` (`action_id`),
  KEY `logger_table.api_type_id.fk_idx` (`api_type_id`),
  KEY `logger_table.location_id.fk` (`location_id`),
  KEY `logger_table.activity_id.fk` (`activity_id`),
  KEY `logger_table.severity_id.fk` (`severity_id`),
  KEY `logger_table.created_user_id.fk` (`created_user_id`),
  KEY `logger_table.profile_id.fk` (`profile_id`),
  KEY `logger_table.updated_user_id.fk` (`updated_user_id`),
  KEY `logger_table.user_id.fk` (`user_id`),
  KEY `logger.smartlink_id.fk_idx` (`smartlink_id`),
  KEY `logger.session.idx` (`session`),
  CONSTRAINT `logger.component_id` FOREIGN KEY (`component_id`) REFERENCES `component`.`component_table` (`component_id`),
  CONSTRAINT `logger.smartlink_id.fk` FOREIGN KEY (`smartlink_id`) REFERENCES `smartlink`.`smartlink_table` (`smartlink_id`),
  CONSTRAINT `logger_table.action_id.fk` FOREIGN KEY (`action_id`) REFERENCES `action`.`action_table` (`action_id`),
  CONSTRAINT `logger_table.activity_id.fk` FOREIGN KEY (`activity_id`) REFERENCES `activity`.`activity_table_old` (`activity_id`),
  CONSTRAINT `logger_table.api_type_id.fk` FOREIGN KEY (`api_type_id`) REFERENCES `api_type`.`api_type_table` (`api_type_id`),
  CONSTRAINT `logger_table.created_user_id.fk` FOREIGN KEY (`created_user_id`) REFERENCES `user`.`user_table` (`user_id`),
  CONSTRAINT `logger_table.field_id.fk` FOREIGN KEY (`field_id`) REFERENCES `field`.`field_table` (`field_id`),
  CONSTRAINT `logger_table.location_id.fk` FOREIGN KEY (`location_id`) REFERENCES `location`.`location_table` (`location_id`),
  CONSTRAINT `logger_table.profile_id.fk` FOREIGN KEY (`profile_id`) REFERENCES `profile`.`profile_table` (`profile_id`),
  CONSTRAINT `logger_table.severity_id.fk` FOREIGN KEY (`severity_id`) REFERENCES `severity_table` (`severity_id`),
  CONSTRAINT `logger_table.state_id.fk` FOREIGN KEY (`state_id`) REFERENCES `dialog_workflow`.`dialog_workflow_state_table` (`state_id`),
  CONSTRAINT `logger_table.status_id.fk` FOREIGN KEY (`status_id`) REFERENCES `status_table` (`id`),
  CONSTRAINT `logger_table.updated_user_id.fk` FOREIGN KEY (`updated_user_id`) REFERENCES `user`.`user_table` (`user_id`),
  CONSTRAINT `logger_table.user_id.fk` FOREIGN KEY (`user_id`) REFERENCES `user`.`user_table` (`user_id`),
  CONSTRAINT `logger_table.variable_id.fk` FOREIGN KEY (`variable_id`) REFERENCES `field`.`variable_table` (`variable_id`)
) ENGINE=InnoDB AUTO_INCREMENT=123663 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='We should take into account that the logger_table records can be deleted. so we keep important data elsewhere. i.e stars in profile_tabe, old_field_value in fields?';
