USE logger;
CREATE OR REPLACE VIEW logger.`logger_general_recent_view` AS 
SELECT
          logger.`logger_id` AS `logger_id`,
        logger.created_timestamp,
        `logger`.`logger`.`server_ip_v4` AS `server_ip_v4`,
        `logger`.`logger`.`server_ip_v6` AS `server_ip_v6`,
        `logger`.`logger`.`component_id` AS `component_id`,
        `logger`.`logger`.`name` AS `name`,
        `logger`.`logger`.`programming_language_id` AS `programming_language_id`,
        `logger`.`logger`.`programming_language` AS `programming_language`
FROM logger.logger_general_view logger
LIMIT 10
;

SELECT * FROM logger_general_recent_view;
