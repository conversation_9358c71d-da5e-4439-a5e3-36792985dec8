USE logger;
CREATE OR REPLACE VIEW logger.`logger_general_view` AS 
SELECT 
        `logger`.`logger`.`logger_id` AS `logger_id`,
        `logger`.`logger`.`created_timestamp` AS `created_timestamp`,
        `logger`.`logger`.`server_ip_v4` AS `server_ip_v4`,
        `logger`.`logger`.`server_ip_v6` AS `server_ip_v6`,
        `logger`.`logger`.`component_id` AS `component_id`,
        `component`.`name` AS `name`,
        `component`.`programming_language_id` AS `programming_language_id`,
        `component`.`programming_language` AS `programming_language`
FROM logger.logger_view logger
LEFT JOIN component.component_table `component` ON `component`.component_id=logger.component_id
ORDER BY logger.created_timestamp DESC
-- LIMIT 10
;
