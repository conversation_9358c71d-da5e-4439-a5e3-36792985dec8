USE `logger`;
CREATE ALGORITHM = UNDEFINED DEFINER = `bubbelz`@`%` SQL SECURITY DEFINER VIEW `logger_function_start_end_view` AS
SELECT `logger_table`.`logger_id`                      AS `logger_id`,
       `logger_table`.`created_timestamp`              AS `created_timestamp`,
       `logger_table`.`severity_id`                    AS `severity_id`,
       `severity_ml_table`.`description`               AS `severity_description`,
       `logger_table`.`component_id`                   AS `component_id`,
       `logger_table`.`component_name`                 AS `logger.component_name`,
       `logger_table`.`function_name`                  AS `function_name`,
       `logger_table`.`developer_email`                AS `logger.developer_email`,
       logger_table.session_id,
       logger_table.process_id,
       logger_table.thread_id,
       `component`.`component_table`.`developer_email` AS `component.developer_email`,
       `component`.`component_table`.`name`            AS `component.component_name`
FROM ((`logger_table`
    JOIN `severity_ml_table` ON ((`severity_ml_table`.`severity_id` = `logger_table`.`severity_id`)))
    JOIN `component`.`component_table`
      ON ((`component`.`component_table`.`component_id` = `logger_table`.`component_id`)))
WHERE (((`logger_table`.`severity_id` = 400)
    OR (`logger_table`.`severity_id` = 402))
    AND (`logger_table`.`component_id` IS NOT NULL)
    AND (`severity_ml_table`.`lang_code` = 'en')
    AND (`logger_table`.`created_timestamp` > '2023-11-01'))
