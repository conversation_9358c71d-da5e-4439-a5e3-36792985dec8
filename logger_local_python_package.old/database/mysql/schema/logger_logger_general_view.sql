CREATE 
    ALGORITHM = UNDEFINED 
    DEFINER = `bubbelz`@`%` 
    SQL SECURITY DEFINER
VIEW `logger`.`logger_logger_general_view` AS
    SELECT 
        `logger`.`logger_table`.`logger_id` AS `logger_id`,
        `logger`.`logger_table`.`client_ip_v4` AS `client_ip_v4`,
        `logger`.`logger_table`.`client_ip_v6` AS `client_ip_v6`,
        `logger`.`logger_table`.`server_ip_v4` AS `server_ip_v4`,
        `logger`.`logger_table`.`server_ip_v6` AS `server_ip_v6`,
        `logger`.`logger_table`.`location_id` AS `location_id`,
        `logger`.`logger_table`.`user_id` AS `user_id`,
        `logger`.`logger_table`.`profile_id` AS `profile_id`,
        `logger`.`logger_table`.`activity` AS `activity`,
        `logger`.`logger_table`.`activity_id` AS `activity_id`,
        `logger`.`logger_table`.`message` AS `message`,
        `logger`.`logger_table`.`payload` AS `payload`,
        `logger`.`logger_table`.`component_id` AS `component_id`,
        `logger`.`logger_table`.`error_stack` AS `error_stack`,
        `logger`.`logger_table`.`severity_id` AS `severity_id`,
        `logger`.`logger_table`.`status_id` AS `status_id`,
        `logger`.`logger_table`.`group_id` AS `group_id`,
        `logger`.`logger_table`.`relationship_type_id` AS `relationship_type_id`,
        `logger`.`logger_table`.`timestamp` AS `timestamp`,
        `logger`.`logger_table`.`state_id` AS `state_id`,
        `logger`.`logger_table`.`variable_id` AS `variable_id`,
        `logger`.`logger_table`.`variable_value_old` AS `variable_value_old`,
        `logger`.`logger_table`.`variable_value_new` AS `variable_value_new`,
        `logger`.`logger_table`.`created_timestamp` AS `created_timestamp`,
        `logger`.`logger_table`.`created_user_id` AS `created_user_id`,
        `logger`.`logger_table`.`updated_timestamp` AS `updated_timestamp`,
        `logger`.`logger_table`.`updated_user_id` AS `updated_user_id`,
        `logger`.`logger_table`.`start_datetime` AS `start_datetime`,
        `logger`.`logger_table`.`end_datetime` AS `end_datetime`,
        `component`.`component_table`.`name` AS `name`
    FROM
        (`logger`.`logger_table`
        JOIN `component`.`component_table`)
    WHERE
        ((`logger`.`logger_table`.`component_id` = `component`.`component_table`.`component_id`)
            AND (`component`.`component_table`.`name` = 'logger'))
    ORDER BY `logger`.`logger_table`.`timestamp` DESC