CREATE VIEW logger.logger_logger_view as
SELECT logger_table.*, component_table.name, component_table.computer_language
FROM logger.logger_table,
     component.component_table
WHERE logger_table.component_id = component_table.id
  AND component.component_table.name = 'logger'
ORDER by timestamp desc;

GRANT SELECT ON logger.logger_logger_view TO 'tal.b'@'%';
GRANT SHOW VIEW ON logger.logger_logger_view TO 'tal.b'@'%';
