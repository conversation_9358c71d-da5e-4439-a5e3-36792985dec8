CREATE SCHEMA IF NOT EXISTS logger;
USE logger;

ALTER TABLE `logger`.`severity_table`
    ADD COLUMN `level` SMALLINT UNSIGNED NOT NULL AFTER `severityid`;

CREATE TABLE IF NOT EXISTS severity_table
(

    severity_id       SMALLINT UNSIGNED UNIQUE AUTO_INCREMENT COMMENT "PK: severity_id",
    `level`           SMALLINT UNSIGNED                   NOT NULL,

    created_timestamp TIMESTAMP DEFAULT NOW()             NOT NULL,
    created_user_id   BIGINT UNSIGNED                     NOT NULL,
    updated_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_user_id   BIGINT UNSIGNED                     NOT NULL,
    start_datetime    TIMESTAMP DEFAULT NOW()             NOT NULL,
    end_datetime      TIMESTAMP DEFAULT NULL,

    PRIMARY KEY (id),
    FOREIGN KEY (created_user_id) REFERENCES user.user_table (user_id),
    FOR<PERSON><PERSON><PERSON> KEY (updated_user_id) REFERENCES user.user_table (user_id)
);

ALTER TABLE `logger`.`severity_table`
    ADD UNIQUE INDEX `level_UNIQUE` (`level` ASC) VISIBLE;

CREATE TABLE IF NOT EXISTS severity_table_ml
(

    id          INT UNSIGNED AUTO_INCREMENT COMMENT "PK:severity_ml_id",
    severity_id SMALLINT UNSIGNED NOT NULL,

    lang_code   CHAR(5)           NOT NULL,
    description VARCHAR(250)      NULL,

    PRIMARY KEY (id),
    FOREIGN KEY (severity_id) REFERENCES logger.severity_table (id)
);
