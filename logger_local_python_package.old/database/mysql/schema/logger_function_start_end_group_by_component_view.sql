CREATE 
    ALGORITHM = UNDEFINED 
    DEFINER = `bubbelz`@`%` 
    SQL SECURITY DEFINER
VIEW `logger`.`logger_function_start_end_group_by_component_view` AS
    SELECT 
        `logger`.`logger_function_start_end_view`.`logger.component_name` AS `logger.component_name`,
        `logger`.`logger_function_start_end_view`.`function_name` AS `function_name`,
        `logger`.`logger_function_start_end_view`.`component_id` AS `component_id`,
        `logger`.`logger_function_start_end_view`.`severity_description` AS `severity_description`,
        COUNT(0) AS `COUNT(*)`
    FROM
        `logger`.`logger_function_start_end_view`
    GROUP BY `logger`.`logger_function_start_end_view`.`component_id` , `logger`.`logger_function_start_end_view`.`function_name` , `logger`.`logger_function_start_end_view`.`severity_id`
    ORDER BY `logger`.`logger_function_start_end_view`.`logger.component_name` , `logger`.`logger_function_start_end_view`.`function_name` , `logger`.`logger_function_start_end_view`.`severity_id`