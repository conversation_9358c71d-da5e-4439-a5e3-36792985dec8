CREATE SCHEMA IF NOT EXISTS logger;
USE logger;

<PERSON>TE<PERSON> TABLE `logger`.`logger_table`
    ADD COLUMN `message`              VARCHAR(2500) NOT NULL AFTER `activity_id`,
    ADD COLUMN `record`               VARCHAR(2500) NOT NULL AFTER `message`,
    ADD COLUMN `group_id`             BIGINT        NULL DEFAULT NULL AFTER `status_id`,
    ADD COLUMN `relationship_type_id` INT           NULL DEFAULT NULL AFTER `group_id`;

ALTER TABLE `logger`.`logger_table`
    ADD COLUMN `record` VARCHAR(2500) NOT NULL AFTER `message`;

CREATE TABLE IF NOT EXISTS logger_table
(

    id                     INT UNSIGNED UNIQUE AUTO_INCREMENT COMMENT "PK: logger_id",

    -- Can be generated by any logger
    timestamp              TIMESTAMP        DEFAULT CURRENT_TIMESTAMP NOT NULL,
    server_ip_v4           CHAR(15)                                   NULL,
    server_ip_v6           CHAR(45)                                   NULL,
    error_stack            TEXT                                       NULL,

    -- Can be generated by logger-remote
    location_id            BIGINT UNSIGNED                            NULL,
    client_ip_v4           CHAR(15)                                   NULL,
    client_ip_v6           CHAR(45)                                   NULL,

    -- Generated by the method name
    severity_id            SMALLINT UNSIGNED                          NULL,

    -- Send by the developer in most cases
    status_id              TINYINT UNSIGNED DEFAULT NULL,
    `message`              VARCHAR(2500)                              NOT NULL, -- Message sent by the developer
    `record`               TEXT                                       NOT NULL, -- Record Object
    payload                TEXT,                                                -- HTML Payload
    component_id           INT UNSIGNED,                                        -- MySQL component_table
    activity               VARCHAR(255),

    -- Ids sent by the developer in the Record Object
    user_id                BIGINT UNSIGNED                            NULL,
    profile_id             BIGINT UNSIGNED                            NULL,
    activity_id            INT UNSIGNED,
    `group_id`             BIGINT                                     NULL DEFAULT NULL,
    `relationship_type_id` INT                                        NULL DEFAULT NULL,

    -- Dialog Workflow
    state_id               INT UNSIGNED                               NOT NULL DEFAULT 0,
    variable_id            INT UNSIGNED,
    variable_value_old     TEXT                                       NULL,
    variable_value_new     TEXT                                       NULL,

    developer_email_addresss VARCHAR(255) NULL,

    -- Table prefix fields
    created_timestamp      TIMESTAMP        DEFAULT NOW()             NOT NULL,
    created_user_id        BIGINT UNSIGNED                            NULL,
    updated_timestamp      TIMESTAMP        DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_user_id        BIGINT UNSIGNED                            NULL,
    start_datetime         TIMESTAMP        DEFAULT NOW()             NOT NULL,
    end_datetime           TIMESTAMP        DEFAULT NULL,

    PRIMARY KEY (id),

    FOREIGN KEY (profile_id) REFERENCES profile.profile_table (profile_id),
    FOREIGN KEY (state_id) REFERENCES dialog_workflow.dialog_workflow_state_table (state_id),
    FOREIGN KEY (variable_id) REFERENCES dialog_workflow.variable_table (variable_id),
    FOREIGN KEY (location_id) REFERENCES location.location_table (location_id),
    FOREIGN KEY (user_id) REFERENCES user.user_table (user_id),
    FOREIGN KEY (component_id) REFERENCES component.component_table (compoentn_id),
    FOREIGN KEY (activity_id) REFERENCES activity.activity_table (activity_id),
    FOREIGN KEY (severity_id) REFERENCES logger.severity_table (severity_id),
    FOREIGN KEY (created_user_id) REFERENCES user.user_table (user_id),
    FOREIGN KEY (updated_user_id) REFERENCES user.user_table (user_id)

);

USE logger;
DROP VIEW logger.`logger_view`;
CREATE ALGORITHM = UNDEFINED DEFINER =`tal`@`%` SQL SECURITY DEFINER VIEW logger.`logger_view` AS
SELECT *
FROM logger.`logger_table`
WHERE ((`logger_table`.`start_datetime` <= curtime()) and
       ((`logger_table`.`end_datetime` is null) or (`logger_table`.`end_datetime` >= curtime())))
ORDER BY id DESC
;

CREATE ALGORITHM = UNDEFINED DEFINER =`tal`@`%` SQL SECURITY DEFINER VIEW logger.`logger_view_100` AS
SELECT *
FROM logger.`logger_table`
WHERE ((`logger_table`.`start_datetime` <= curtime()) and
       ((`logger_table`.`end_datetime` is null) or (`logger_table`.`end_datetime` >= curtime())))
ORDER BY id DESC
LIMIT 100
;

/*
--DROP VIEW `logger_view`;
CREATE ALGORITHM=UNDEFINED DEFINER=`tal.b`@`%` SQL SECURITY DEFINER VIEW `logger_view` AS 
    SELECT `logger_table`.`id` AS `id`, -- All fields we want to filter in WHERE should be identical
      `logger_table`.`client_ip_v4` AS `Client IP v4`,
      `logger_table`.`client_ip_v6` AS `Client IP v6`,
      `logger_table`.`server_ip_v4` AS `Server IP v4`,
      `logger_table`.`server_ip_v6` AS `Server IP v6`,
      `logger_table`.`location_id` AS `location id`,`logger_table`.`user_id` AS `user id`,`logger_table`.`profile_id` AS `profile id`,`logger_table`.`activity` AS `activity`,`logger_table`.`activity_id` AS `activity id`,`logger_table`.`payload` AS `payload`,`logger_table`.`component_id` AS `component id`,`logger_table`.`severity_id` AS `severity id`,`logger_table`.`state_id` AS `state id`,`logger_table`.`variable_id` AS `variable id`,`logger_table`.`variable_value_old` AS `old value`,`logger_table`.`variable_value_new` AS `new value`,`logger_table`.`created_timestamp` AS `created timestamp`,`logger_table`.`created_user_id` AS `created user id`,`logger_table`.`updated_timestamp` AS `updated timestamp`,`logger_table`.`updated_user_id` AS `updated user id`
    FROM `logger_table`
    WHERE ((`logger_table`.`start_datetime` <= curtime()) and ((`logger_table`.`end_datetime` is null) or (`logger_table`.`end_datetime` >= curtime())));
*/
