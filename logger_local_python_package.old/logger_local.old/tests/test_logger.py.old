import json
import os
import re
import time
import traceback
from datetime import datetime

import pytest
from python_sdk_remote.utilities import get_environment_name
from url_remote.environment_name_enum import Environment<PERSON>ame

from src.Connector import get_connection
from src.LoggerComponentEnum import Logger<PERSON>omponentEnum
from src.LoggerLocal import Logger, obfuscate_log_dict
from src.MessageSeverity import MessageSeverity
from .constants import (logger_test_object, LOGGER_COMPONENT_ID, LOGGER_COMPONENT_NAME,
                        LOGGER_COMPONENT_CATEGORY, LOGGER_DEVELOPER_EMAIL)

# TODO: test LOGGER_CONFIGURATION_JSON_PATH

TEST_COMPONENT_ID_1 = 5000002
TEST_COMPONENT_ID_2 = 5000003

DEBUG_MINIMUM_SEVERITY_TEST = "debug"


@pytest.fixture(scope="module")
def logger():
    logger = Logger.create_logger(object=logger_test_object, level=DEBUG_MINIMUM_SEVERITY_TEST,
                                  ignore_cached_logger=True)
    logger._is_write_to_sql = True
    yield logger
    logger._is_write_to_sql = False


def get_test_connection():
    time.sleep(3)
    connection = get_connection(schema_name="logger")
    connection.commit()
    return connection


# TODO Shall we develop get_log( column: str, value: str, select_clause: str = "severity_id,message")?
# TODO Shall we move this to test_utils.py or even better make something more generic and not only for logger? i.e. get_record_tuple()
def get_log(column_name: str, payload_object: dict, select_clause: str = "severity_id,message") -> tuple:
    connection = get_test_connection()
    cursor = connection.cursor()
    sql_query = f"""SELECT {select_clause} FROM logger.logger_view
                    WHERE {column_name} = %s LIMIT 1;"""
    cursor.execute(sql_query, (payload_object[column_name],))
    result = cursor.fetchone()
    # TODO Is correct (raise or return None)?
    if result is None:
        raise AssertionError(f"No matching record found for {column_name} = {payload_object[column_name]} when looking for {select_clause}")
    cursor.close()
    return result


# TODO This function should replace the get_log() function
# TODO Move this function to a new python-sdk-remote repo
def get_record_by_int_tuple(table_view_name: str, column_name: str, value: int, select_clause: str = "severity_id,message,error_stack") -> tuple:
    connection = get_test_connection()
    cursor = connection.cursor()
    sql_query = f"""SELECT {select_clause} FROM logger.logger_view
                    WHERE {column_name} = %s LIMIT 1;"""
    cursor.execute(sql_query, (value,))
    result = cursor.fetchone()
    # TODO Is correct (raise or return None)?
    if result is None:
        raise AssertionError(f"No matching record found for {column_name} = {value} when looking for {select_clause}")
    cursor.close()
    return result


# TODO Add test of scenario where LOGGER_MINIMUM_SEVERITY was not defined
def test_log_with_only_logger_object(logger):
    object_to_insert_1 = {
        'payload': 'log from python -object_1 check',
        'password': 'very secret password'
    }
    logger.info(object=object_to_insert_1)
    result = get_log('payload', object_to_insert_1, select_clause="severity_id,record")  # noqa E501
    assert result[0] == MessageSeverity.Information.value
    password = json.loads(result[1])['password']
    if get_environment_name() == EnvironmentName.PLAY1.value:
        assert password == 'very secret password'
    else:
        assert password == '***'


def test_error_with_only_logger_object(logger):
    object_to_insert_2 = {
        'payload': 'payload from error python -object_2',
    }
    logger.error(object=object_to_insert_2)
    result = get_log('payload', object_to_insert_2)
    assert result[0] == MessageSeverity.Error.value


def test_verbose_with_only_logger_object(logger):
    object_to_insert_3 = {
        'client_ip_v4': 'ipv4-py',
        'client_ip_v6': 'ipv6-py',
        'latitude': 32,
        'longitude': 35,
        'variable_id': 5000001,
        'variable_value_old': 'variable_value_old-python-object_3',
        'variable_value_new': 'variable_value_new-python',
    }
    logger.verbose(object=object_to_insert_3)
    result = get_log('variable_value_old', object_to_insert_3)
    assert result[0] == MessageSeverity.Verbose.value


def test_warn_with_only_logger_object(logger):
    object_to_insert_4 = {
        'client_ip_v4': 'ipv4-py',
        'client_ip_v6': 'ipv6-py',
        'latitude': 32,
        'longitude': 35,
        'activity': 'test from python',
        'activity_id': 5000001,
        'payload': 'payload from python -object_4',
        'variable_value_new': 'variable_value_new-python',
        'created_user_id': 5000001,
        'updated_user_id': 5000001
    }
    logger.warning(object=object_to_insert_4)
    result = get_log('payload', object_to_insert_4)
    assert result[0] == MessageSeverity.Warning.value


def test_add_message(logger):
    # option to insert only message
    message = 'only message error from python'
    logger.error(message)
    result = get_log('message', {'message': message})
    assert result[0] == MessageSeverity.Error.value


def test_debug_with_only_logger_object(logger):
    object_to_insert5 = {
        'payload': "Test python!!! check for debug insert"
    }
    logger.debug(object=object_to_insert5)
    result = get_log('payload', object_to_insert5)
    assert result[0] == MessageSeverity.Debug.value


def test_start_with_only_logger_object(logger):
    object_to_insert6 = {
        'payload': "Test python!!! check for start insert"
    }
    logger.start(object=object_to_insert6)
    result = get_log('payload', object_to_insert6)
    assert result[0] == MessageSeverity.Start.value


def test_end_with_only_logger_object(logger):
    object_to_insert7 = {
        'payload': "Test python!!! check for end insert",
    }
    logger.end(object=object_to_insert7)
    result = get_log('payload', object_to_insert7)
    assert result[0] == MessageSeverity.End.value


def test_init_with_only_logger_object(logger):
    message = "Test python!!! check for init insert"
    logger.init(message)
    result = get_log('message', {'message': message})
    assert result[0] == MessageSeverity.Init.value


def test_exception_with_payload(logger):
    stack_trace = ""
    message = "Test python!!! check for exception insert"
    logger_id: int = None;
    try:
        5 / 0
    except Exception as exception:
        logger_id = logger.error(object={"exception": exception, "message": message})  # noqa E501
        stack_trace = str(traceback.format_exception(
            type(exception), exception, exception.__traceback__))

    if logger_id:
        connection = get_test_connection()
        cursor = connection.cursor()

        # New way
        record_tuple = get_record_by_int_tuple('logger.logger_view', 'logger_id', logger_id, select_clause="severity_id, message, error_stack")
        assert record_tuple is not None, "test_exception_with_payload() No matching record found"  # noqa E501
        print("logger-local test_logger.py version={__version__} test_with_payload() record_tuple={record_tuple}) assert record_tuple[0] >= MessageSeverity.Error.value")
        assert record_tuple[1] == message
        assert record_tuple[2].startswith(r"['Traceback (most recent call last):\n'")

        # Old way (TODO Shall we remove it?)
        escaped_stack_trace = re.escape(stack_trace)
        pattern = f"%{escaped_stack_trace}%"
        # TODO Replace all SELECT with LIKE with get_record_tuple(
        sql = ("SELECT severity_id, message FROM logger.logger_view "
            "WHERE error_stack LIKE %s "
            "ORDER BY timestamp DESC LIMIT 1;")
        cursor.execute(sql, (pattern,))

        result = cursor.fetchone()
        assert result is not None, "test_logger.py test_exception_with_payload() No matching record found"
        assert result[0] >= MessageSeverity.Error.value
        assert result[1] == message
    else:
        print("test_exception_with_payload() logger_id is None, the logger.error() did not return a valid logger_id.")
        # raise AssertionError("logger_id is None, the logger.error() did not return a valid logger_id.")


def test_exception_with_only_logger_object(logger):
    stack_trace = ""
    try:
        5 / 0
    except Exception as exception:
        logger.error(object=exception)
        stack_trace = str(traceback.format_exception(
            type(exception), exception, exception.__traceback__))

    connection = get_test_connection()
    cursor = connection.cursor()

    # TODO Replace all SELECT with LIKE with get_record_tuple(
    escaped_stack_trace = re.escape(stack_trace)
    pattern = f"%{escaped_stack_trace}%"
    sql = ("SELECT severity_id FROM logger.logger_view "
           "WHERE error_stack LIKE %s "
           "ORDER BY timestamp DESC LIMIT 1;")
    cursor.execute(sql, (pattern,))

    result = cursor.fetchone()
    assert result is not None, "test_exception_with_only_logger_object() No matching record found"
    assert result[0] >= MessageSeverity.Error.value


def test_error(logger):
    object_to_insert9 = {
        'payload': 'payload from error python -object_9'
    }
    msg = "check for error with both object and message"
    logger.error(msg, object=object_to_insert9)

    result = get_log('payload', object_to_insert9)
    assert result[0] == MessageSeverity.Error.value
    assert result[1] == msg


def test_start(logger):
    object_to_insert10 = {
        'payload': 'payload from start python -object_10'

    }
    msg = "check for start with both object and message"
    logger.start(msg, object=object_to_insert10)
    result = get_log('payload', object_to_insert10)
    assert result[0] == MessageSeverity.Start.value
    assert result[1] == msg


def test_end(logger):
    object_to_insert11 = {
        'payload': 'payload from end python -object_11'

    }
    msg = "check for end with both object and message"
    logger.end(msg, object=object_to_insert11)
    result = get_log('payload', object_to_insert11)
    assert result[0] == MessageSeverity.End.value
    assert result[1] == msg


def test_debug(logger):
    object_to_insert12 = {
        'payload': 'payload from debug python -object_12'

    }
    msg = "check for debug with both object and message"
    logger.debug(msg, object=object_to_insert12)
    result = get_log('payload', object_to_insert12)
    assert result[0] == MessageSeverity.Debug.value
    assert result[1] == msg


def test_log(logger):
    object_to_insert13 = {
        'payload': 'payload from info python -object_13'

    }
    msg = "check for info with both object and message"
    logger.info(msg, object=object_to_insert13)
    result = get_log('payload', object_to_insert13)
    assert result[0] == MessageSeverity.Information.value
    assert result[1] == msg


def test_init(logger):
    object_to_insert14 = {
        'payload': 'payload from init python -object_14'

    }
    msg = "check for init with both object and message"
    logger.init(msg, object=object_to_insert14)
    result = get_log('payload', object_to_insert14)
    assert result[0] == MessageSeverity.Init.value
    assert result[1] == msg


def test_exception(logger):
    stack_trace = ""
    try:
        5 / None  # noqa
    except Exception as exception:
        logger.error("exception check", object=exception)
        stack_trace = str(traceback.format_exception(
            type(exception), exception, exception.__traceback__))

    connection = get_test_connection()
    cursor = connection.cursor()

    # TODO Replace all SELECT with LIKE with get_record_tuple(
    escaped_stack_trace = re.escape(stack_trace)
    pattern = f"%{escaped_stack_trace}%"
    sql = ("SELECT severity_id,message FROM logger.logger_view "
           "WHERE error_stack LIKE %s ORDER BY timestamp DESC LIMIT 1;")
    cursor.execute(sql, (pattern,))
    result = cursor.fetchone()
    assert result[0] >= MessageSeverity.Error.value
    assert result[1] == "exception check"


def test_check_function(logger):
    datetime_object = datetime.now()
    object_to_insert15 = {
        'payload': "check python",
        'component_id': LOGGER_COMPONENT_ID,
        'a': 5,
        'b': 6,
        'datetime_object': datetime_object  # test inserting object
    }
    logger.start(object=object_to_insert15)
    connection = get_test_connection()
    cursor = connection.cursor()

    sql_query = """
    SELECT component_id, record
    FROM logger.logger_view
    WHERE component_name = 'Logger Python'  -- ignore other logs that may have been inserted. TODO: in all tests
    ORDER BY logger_id desc limit 1;
    """
    cursor.execute(sql_query)
    result = cursor.fetchone()
    assert result is not None, "No matching record found"
    assert result[0] == LOGGER_COMPONENT_ID

    received_record = json.loads(result[1])
    expected_record = {"a": "5", "b": 6, "datetime_object": datetime_object, "severity_name": "START"}
    for k, v in expected_record.items():  # received_record may contain more fields than expected_record
        try:
            assert k in received_record
            assert received_record[k] == str(v)
        except AssertionError:
            print(f"object_to_insert15, key: {k}, expected: {v}, received: {received_record.get(k, received_record)}")
            raise

    object_to_insert16 = {
        'component_id': LOGGER_COMPONENT_ID,
        'payload': "check python",
        'return': 9,
    }
    logger.end(object=object_to_insert16)

    time.sleep(3)
    # sync the connection before querying
    connection.commit()

    cursor.execute(sql_query)
    result = cursor.fetchone()
    assert result is not None, "test_check_function() No matching record found"
    assert result[0] == LOGGER_COMPONENT_ID

    received_record = json.loads(result[1])
    expected_record = {"return": 9, "severity_name": "END"}
    for k, v in expected_record.items():  # received_record may contain more fields than expected_record
        try:
            assert k in received_record
            assert received_record[k] == str(v)
        except AssertionError:
            print(f"object_to_insert16, key: {k}, expected: {v}, received: {received_record.get(k, received_record)}")
            raise


def test_check_init_component_enum(logger):
    object_to_insert17 = {
        'payload': "check python init with component",
        'component_id': LOGGER_COMPONENT_ID,
        'component_name': LOGGER_COMPONENT_NAME,
        "component_category": LoggerComponentEnum.ComponentCategory.Unit_Test.value,
        'testing_framework': LoggerComponentEnum.testingFramework.pytest.value,
    }
    logger.init(object=object_to_insert17)
    select_clause = "component_name,component_category,testing_framework"
    result = get_log('payload', object_to_insert17, select_clause=select_clause)
    assert result[0] == LOGGER_COMPONENT_NAME
    # assert result[1] == LoggerComponentEnum.ComponentCategory.Unit_Test.value
    # assert result[2] == LoggerComponentEnum.testingFramework.pytest.value

    object_to_insert17['message'] = 'check if component saved'
    logger.info(object_to_insert17['message'])
    result = get_log('message', object_to_insert17, select_clause=select_clause)
    assert result[0] == LOGGER_COMPONENT_NAME
    # assert result[1] == LoggerComponentEnum.ComponentCategory.Unit_Test.value
    # assert result[2] == LoggerComponentEnum.testingFramework.pytest.value


def test_check_init_two_different_loggers(logger):
    obj1 = {
        'component_id': TEST_COMPONENT_ID_1,
        'component_name': "check",
        'component_category': LOGGER_COMPONENT_CATEGORY,
        "developer_email": LOGGER_DEVELOPER_EMAIL,
        "testing_framework": LoggerComponentEnum.testingFramework.pytest.value
    }
    obj2 = {
        'component_id': TEST_COMPONENT_ID_2,
        'component_name': "check2",
        'component_category': LOGGER_COMPONENT_CATEGORY,
        "developer_email": LOGGER_DEVELOPER_EMAIL,
        "testing_framework": LoggerComponentEnum.testingFramework.pytest.value
    }
    logger1 = Logger.create_logger(object=obj1, level="info", ignore_cached_logger=True)
    logger2 = Logger.create_logger(object=obj2, level="info", ignore_cached_logger=True)
    logger1._is_write_to_sql = True
    logger2._is_write_to_sql = True

    msg1 = "check logger 1 " + str(datetime.now())
    logger1.info(msg1)
    comp1 = get_log('message', {'message': msg1}, select_clause="component_id")

    msg2 = "check logger 2 " + str(datetime.now())
    logger2.info(msg2)
    comp2 = get_log('message', {'message': msg2}, select_clause="component_id")

    assert comp1[0] == TEST_COMPONENT_ID_1
    assert comp2[0] == TEST_COMPONENT_ID_2


# TODO Move to python-sdk as we want to use it not only in logger
def test_obfuscate_log_dict():
    environment = get_environment_name()
    os.environ["ENVIRONMENT_NAME"] = "prod1"

    log_dict = {
        "user_email": "<EMAIL>",
        "user_password": "supersecret",
        "login_time": "2024-06-03T12:34:56",
        "user_name": "John Doe",
    }
    expected_output = {
        "user_email": "***",
        "user_password": "***",
        "login_time": "2024-06-03T12:34:56",
        "user_name": "***",
    }
    assert obfuscate_log_dict(log_dict) == expected_output

    log_dict = {
        "user_email": "<EMAIL>",
        "details": {
            "token": "abcdef123456",
            "address": "123 Main St",
            "phone_number": "************"
        },
        "component_id": 3
    }
    expected_output = {
        "user_email": "***",
        "details": {
            "token": "***",
            "address": "***",
            "phone_number": "***"
        },
        "component_id": 3
    }
    assert obfuscate_log_dict(log_dict) == expected_output

    log_dict = {
        "login_time": "2024-06-03T12:34:56",
        "status": "success",
        "session_id": "xyz123"
    }
    expected_output = {
        "login_time": "2024-06-03T12:34:56",
        "status": "success",
        "session_id": "xyz123"
    }
    assert obfuscate_log_dict(log_dict) == expected_output

    log_dict = {
        "user_email": "<EMAIL>",
        "login_time": "2024-06-03T12:34:56",
        "details": {
            "token": "abcdef123456",
            "address": "123 Main St",
            "name_last": "Doe",
            "test": "test"
        }
    }
    expected_output = {
        "user_email": "***",
        "login_time": "2024-06-03T12:34:56",
        "details": {
            "token": "***",
            "address": "***",
            "name_last": "***",
            "test": "test"
        }
    }
    assert obfuscate_log_dict(log_dict) == expected_output

    os.environ["ENVIRONMENT_NAME"] = environment
