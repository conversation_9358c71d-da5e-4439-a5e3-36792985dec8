from database_mysql_local.generic_crud import GenericCRUD
from logger_local.LoggerComponentEnum import Lo<PERSON><PERSON>omponentEnum
from logger_local.LoggerLocal import Logger
from python_sdk_local.http_response import handler_decorator
from python_sdk_local.service_response import ServiceResponse


GOOGLE_ACCOUNT_SERVERLESS_COMPONENT_ID = 292
GOOGLE_ACCOUNT_SERVERLESS_COMPONENT_NAME = "google-account-serverless"
DEVELOPER_EMAIL = "<EMAIL>"
GOOGLE_ACCOUNT_SERVERLESS_CODE_LOGGER_OBJECT = {
    "component_id": GOOGLE_ACCOUNT_SERVERLESS_COMPONENT_ID,
    "component_name": GOOGLE_ACCOUNT_SERVERLESS_COMPONENT_NAME,
    "component_category": LoggerComponentEnum.ComponentCategory.Code.value,
    "developer_email_address": DEVELOPER_EMAIL,
}

logger = Logger.create_logger(object=GOOGLE_ACCOUNT_SERVERLESS_CODE_LOGGER_OBJECT)


@handler_decorator(logger=logger, is_validate_user_jwt=False)
def google_account_get_code_lambda_handler(request_parameters: dict) -> dict:
    logger.info("request_parameters: " + str(request_parameters))
    access_token = request_parameters.get("code")

    if access_token is None:
        logger.error("Access token is None")
        # print("Access token is None")
        service_response = ServiceResponse(
            # TODO shall we use “message”, “en” to make it simetric or better “message_internal” : “….” directly?
            message_internal={"message": "Access token is None"},
            message_external={
                "en": "Access token is None",
            },
            is_success=False,
            data={},
        )
        http_response_dict = service_response.to_http_response()
        return http_response_dict
    else:
        # print("Access token: " + access_token)
        logger.info("Access token: " + access_token)

    state = request_parameters.get("state")

    if state is None:
        logger.error("State is None")
        service_response = ServiceResponse(
            message_internal={"message": "State is None"},
            # TODO can we avoid sending message_external in such case?
            message_external={"en": "State is None", "he": "State is None"},
            is_success=False,
            data={},
        )
        http_response_dict = service_response.to_http_response()
        return http_response_dict
    else:
        # print("State: " + state)
        logger.info("State: " + state)

    logger.info(
        "Updating user_external table with access_token: "
        + access_token
        + " and state: "
        + state
    )

    user_external_id = GenericCRUD(
        default_schema_name="user_external", default_table_name="user_external_table"
    ).select_one_value_by_column_and_value(
        schema_name="user_external",
        view_table_name="user_external_view",
        select_clause_value="user_external_id",
        column_name="oauth_state",
        column_value=state,
    )

    updated_user_external_rows = GenericCRUD(
        default_schema_name="user_external", default_table_name="token__user_external", is_test_data=True
    ).update_by_column_and_value(
        schema_name="user_external",
        table_name="token__user_external_old_table",
        column_name="user_external_id",
        column_value=user_external_id,
        data_dict={"access_token": access_token},
    )
    logger.info("Updated user_external rows: " + str(updated_user_external_rows))

    # updated_user_external_rows = GenericCRUD(default_schema_name="user_external", default_table_name="token__user_external_table").insert(
    #     column_name="oauth_state", column_value=state, data_dict={"access_token": access_token})

    # print("Updated user_external rows len: " + str(updated_user_external_rows))

    if updated_user_external_rows == 1:
        service_response = ServiceResponse(
            message_internal={
                "message": f"Successfully authenticated - access_token saved in the database for the given state access_token, access_token: {access_token}, state: {state}"  # noqa: E501
            },  # noqa: E501
            message_external={
                "en": "Successfully authenticated - access_token saved in the database for the given state access_token",
                "he": "ההתחברות בוצעה בהצלחה - access_token נשמר במסד הנתונים עבור ה-state שניתן",
            },  # noqa: E501
            is_success=True,
            data={"access_token": access_token},
        )

        http_response_dict = service_response.to_http_response()
    elif updated_user_external_rows == 0:
        service_response = ServiceResponse(
            message_internal={
                "message": "Failed to authenticate or you are already authenticated"
            },  # noqa: E501
            message_external={
                "en": "Failed to authenticate or you are already authenticated",
                "he": "ההתחברות נכשלה או שאתה כבר מחובר",
            },  # noqa: E501
            is_success=True,
        )

        http_response_dict = service_response.to_http_response()
    else:
        logger.error("Multiple rows were updated by state=" + state)
        # print("Multiple rows were updated by state=" + state)
        service_response = ServiceResponse(
            message_internal={"message": "Multiple rows were updated"},  # noqa: E501
            message_external={
                "en": "Multiple rows were updated",
                "he": "מספר שורות עודכנו על ידי ",
            },  # noqa: E501
        )

        http_response_dict = service_response.to_http_response()

    logger.info("Response dict: " + str(http_response_dict))
    # print("Response dict: " + str(response_dict))
    return http_response_dict
