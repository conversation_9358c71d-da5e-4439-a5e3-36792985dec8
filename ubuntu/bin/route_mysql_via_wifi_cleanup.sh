#!/bin/bash
# TODO Move this code to route_mysql_via_wifi.sh when parameter --cleanup is used
DB_HOST="mysql-db.dvlp1.circ.zone"
HOSTGATOR_HOST="gator4300.hostgator.com"
#HOSTGATOR_HOST="nfhostingapp.com"

ROUTING_TABLE_NAME="mysql-db-dvlp1-wifi-route"

DB_IP=$(getent hosts "$DB_HOST" | awk '{ print $1 }')
HOSTGATOR_IP=$(getent hosts "$HOSTGATOR_HOST" | awk '{ print $1 }')

sudo ip rule del to "$DB_IP" lookup "$ROUTING_TABLE_NAME"
sudo ip rule del to "$HOSTGATOR_IP" lookup "$ROUTING_TABLE_NAME"

sudo ip route flush table "$ROUTING_TABLE_NAME"

echo "Cleaned up route and rule for $DB_HOST ($DB_IP)"
echo "Cleaned up route and rule for $DB_HOST ($HOSTGATOR_IP)"