#!/bin/bash
# How to route traffic to mysql-db.dvlp1.circ.zone port 3306 via the Wi-Fi NIC, while all other communication is done via the Ethernet/LAN in Ubuntu.


# nmcli device status
# ip link show
WIFI_INTERFACE="wlan0"
WIFI_INTERFACE="wlp0s20f3"
ETH_INTERFACE="eth0"
ETH_INTERFACE="enx00e04c689044"

# ---- Configurable Variables ----
ROUTING_TABLE_ID=100
ROUTING_TABLE_NAME="mysql-db-dvlp1-wifi-route"
DB_HOST="mysql-db.dvlp1.circ.zone"

# https://gator4300.hostgator.com:2096
HOSTGATOR_HOST="gator4300.hostgator.com"
# TODO Maybe returns multiple IPs, we should use the first one
#HOSTGATOR_HOST="nfhostingapp.com"


# ping do not work before and after the routing
# ping $DB_HOST -c 1

# Commented to make the script faster
# echo "sudo traceroute -p 3306 $DB_HOST"
# sudo traceroute -p 3306 $DB_HOST

# ---- Resolve DB Host to IP ----
DB_IP=$(getent hosts "$DB_HOST" | awk '{ print $1 }')
HOSTGATOR_IP=$(getent hosts "$HOSTGATOR_HOST" | awk '{ print $1 }')
echo "HOSTGATOR_IP: $HOSTGATOR_IP"

if [[ -z "$DB_IP" ]]; then
  echo "Failed to resolve $DB_HOST"
  exit 1
fi

echo "Resolved $DB_HOST to $DB_IP"

# ---- Get Wi-Fi Gateway and IP ----
WIFI_IP=$(ip -4 addr show "$WIFI_INTERFACE" | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
WIFI_GW=$(ip route show dev "$WIFI_INTERFACE" | grep default | awk '{ print $3 }')

if [[ -z "$WIFI_GW" || -z "$WIFI_IP" ]]; then
  echo "Failed to get Wi-Fi IP or gateway"
  exit 2
fi

echo "Wi-Fi IP: $WIFI_IP, Gateway: $WIFI_GW"

# ---- Add Custom Routing Table If Not Exists ----
grep -q "$ROUTING_TABLE_ID $ROUTING_TABLE_NAME" /etc/iproute2/rt_tables || echo "$ROUTING_TABLE_ID $ROUTING_TABLE_NAME" | sudo tee -a /etc/iproute2/rt_tables
cat /etc/iproute2/rt_tables
# ---- Set Up Route in Custom Table ----
echo "ip route flush"
sudo ip route flush table "$ROUTING_TABLE_NAME"

echo "ip route add"
# sudo ip route add "$DB_IP" via "$WIFI_GW" dev "$WIFI_INTERFACE" table "$ROUTING_TABLE_NAME"
# Doing again with ROUTING_ID (Maybe after reboot we can use also ROUTING_TABLE_NAME)
sudo ip route add "$DB_IP" via "$WIFI_GW" dev "$WIFI_INTERFACE" table "$ROUTING_TABLE_ID"
# TODO Can we add the hostgator IP to the same routing table?
sudo ip route add "$HOSTGATOR_IP" via "$WIFI_GW" dev "$WIFI_INTERFACE" table "$ROUTING_TABLE_ID"

# ---- Set Up IP Rule ----
echo "ip rule add"
# sudo ip rule add to "$DB_IP" lookup "$ROUTING_TABLE_NAME"
sudo ip rule add to "$DB_IP" lookup "$ROUTING_TABLE_ID"

# TODO Can we add the hostgator IP to the same routing table?
sudo ip rule add to "$HOSTGATOR_IP" lookup "$ROUTING_TABLE_ID"

echo "Traffic to $DB_IP ($DB_HOST) will go via $WIFI_INTERFACE"

# Ping do not work before and after the routing
# ping $DB_HOST -c 1

echo "sudo traceroute -p 3306 $DB_HOST"
# -m max_ttl
sudo traceroute -p 3306 -m 11 $DB_HOST

echo "sudo traceroute -p 3306 $HOSTGATOR_HOST"
# -m max_ttl
sudo traceroute -p 3306 -m 15 $HOSTGATOR_HOST

# TODO tcpdump is too long, we should find a switch to make it shorter
# echo "sudo tcpdump -v -i $WIFI_INTERFACE port 3306"
# sudo tcpdump -v -i $WIFI_INTERFACE port 3306
