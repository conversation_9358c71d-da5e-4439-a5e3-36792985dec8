from logger_local.LoggerLocal import Logger
from logger_local.MetaLogger import module_wrapper

from ..src.constants import OBJECT_TO_INSERT_TEST
from ..src.number_generator import NumberGenerator

TEST_NUMBER_GENERATOR_FUNCTION_NAME = "test_number_generator"

logger = Logger.create_logger(object=OBJECT_TO_INSERT_TEST)

TEST_SCHEMA = "test"
TEST_VIEW = "test_mysql_view"


def test_number_generator():
    number1 = NumberGenerator.get_random_number(TEST_SCHEMA, TEST_VIEW)
    number2 = NumberGenerator.get_random_number(TEST_SCHEMA, TEST_VIEW)
    assert number1 != number2


def test_identifier_generator():
    identifier1 = NumberGenerator.get_random_identifier(TEST_SCHEMA, TEST_VIEW, "name")
    identifier2 = NumberGenerator.get_random_identifier(TEST_SCHEMA, TEST_VIEW, "name")
    assert identifier1 != identifier2


module_wrapper(logger)
