============================= test session starts ==============================
platform linux -- Python 3.13.3, pytest-8.3.5, pluggy-1.5.0
rootdir: /repos/circlez/database-infrastructure-local-python-package/database-infrastructure-local-python-package
configfile: pyproject.toml
plugins: cov-6.2.1, anyio-4.9.0, langsmith-0.4.5
LOGZIO_TOKEN is not set, logs will not be sent to Logz.io
LoggerLocal.py session=98PNXLIEM33OHVP4F2UX9AD6XR0ZUC
Please change developer_email to developer_email_address in the logger object of component_id=177 component_name=circles_number_generator
Please change developer_email to developer_email_address in the logger object of component_id=177 component_name=circles_number_generator
collected 2 items

database_infrastructure_local/tests/test_number_generator.py get_return_variables: get_random_number
get_return_variables: get_random_number success.
get_return_variables: test_number_generator
get_return_variables: test_number_generator success.
.get_return_variables: generate_random_string
get_return_variables: generate_random_string success.
get_return_variables: get_random_identifier
get_return_variables: get_random_identifier success.
get_return_variables: test_identifier_generator
get_return_variables: test_identifier_generator success.
.

- generated xml file: /repos/circlez/database-infrastructure-local-python-package/database-infrastructure-local-python-package/pytest.xml -
================================ tests coverage ================================
_______________ coverage: platform linux, python 3.13.3-final-0 ________________

Name                                                         Stmts   Miss  Cover   Missing
------------------------------------------------------------------------------------------
database_infrastructure_local/src/constants_tests.py             7      7     0%   4-19
database_infrastructure_local/src/generic_crud_abstract.py       5      5     0%   3-23
database_infrastructure_local/src/number_generator.py           42      4    90%   40-41, 69-70
database_infrastructure_local/src/point.py                      15     15     0%   1-29
database_infrastructure_local/src/to_sql_interface.py           48     48     0%   4-116
setup.py                                                         4      4     0%   1-6
------------------------------------------------------------------------------------------
TOTAL                                                          151     83    45%

6 files skipped due to complete coverage.
Coverage XML written to file coverage.xml
========================= 2 passed in 72.99s (0:01:12) =========================
