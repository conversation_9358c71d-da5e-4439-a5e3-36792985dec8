# This file should be in the future instead of setup.py
# https://python-poetry.org/docs/pyproject

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.poetry]
name = "database-infrastructure-local"
version = "0.0.19" # https://pypi.org/project/database-infrastructure-local/
description = "database-infrastructure-local-python-package Python Package"
readme = "README.md"
authors = [
    "Circlez.ai <<EMAIL>>",
]
