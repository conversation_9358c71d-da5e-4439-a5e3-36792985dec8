-- Rename to test_mysql_table.sql
USE test;
CREATE VIEW `test_without_orm_ml_view` AS
SELECT `test_without_orm_ml_table`.`test_without_orm_ml_id` AS `test_without_orm_ml_id`,
       `test_without_orm_ml_table`.`test_without_orm_id`    AS `test_without_orm_id`,
       `test_without_orm_ml_table`.`title`                  AS `title`,
       `test_without_orm_ml_table`.`is_title_approved`      AS `is_title_approved`,
       `test_without_orm_ml_table`.`lang_code`              AS `lang_code`,
       `test_without_orm_ml_table`.`created_timestamp`      AS `created_timestamp`,
       `test_without_orm_ml_table`.`created_user_id`        AS `created_user_id`,
       `test_without_orm_ml_table`.`updated_timestamp`      AS `updated_timestamp`,
       `test_without_orm_ml_table`.`updated_user_id`        AS `updated_user_id`,
       `test_without_orm_ml_table`.`start_timestamp`        AS `start_timestamp`,
       `test_without_orm_ml_table`.`end_timestamp`          AS `end_timestamp`
FROM `test_without_orm_ml_table`
WHERE `test_without_orm_ml_table`.`start_timestamp` <= CURRENT_TIME()
  AND (`test_without_orm_ml_table`.`end_timestamp` IS NULL
    OR `test_without_orm_ml_table`.`end_timestamp` >= CURRENT_TIME())
