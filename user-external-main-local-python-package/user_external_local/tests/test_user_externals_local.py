# When testing using .env file, uncomment below
# from logger_local.MetaLogger import module_wrapper

from dotenv import load_dotenv
from datetime import datetime
from time import sleep
from logger_local.LoggerComponentEnum import LoggerComponentEnum
from logger_local.LoggerLocal import Logger
import mysql.connector
import time


from src.user_externals_local import (
    UserExternalsLocal,
    USER_EXTERNAL_LOCAL_PYTHON_PACKAGE_COMPONENT_ID,
    USER_EXTERNAL_LOCAL_PYTHON_PACKAGE_COMPONENT_NAME,
    DEVELOPER_EMAIL,
)

load_dotenv()

# not used we now generate a user for each test, but kept for reference
# TEST_USER_EXTERNAL = "TEST" + str(datetime.now())
# TEST_STATIC_USER_EXTERNAL = "TEST_STATIC" + str(datetime.now())

USER_EXTERNAL_TEST_LOGGER_OBJECT = {
    "component_id": USER_EXTERNAL_LOCAL_PYTHON_PACKAGE_COMPONENT_ID,
    "component_name": USER_EXTERNAL_LOCAL_PYTHON_PACKAGE_COMPONENT_NAME,
    "component_category": LoggerComponentEnum.ComponentCategory.Unit_Test.value,
    "developer_email": DEVELOPER_EMAIL,
    "testing_framework": LoggerComponentEnum.testingFramework.pytest.value,
}

logger = Logger.create_logger(object=USER_EXTERNAL_TEST_LOGGER_OBJECT)

# TODO If it's possible to use only 1 TEST_PROFILE_ID, use the get_test_profile_id method to get it
# https://github.com/circles-zone/profile-main-local-python-package/blob/d301308fc5e1ffb00ec3333c7803b556636b3cd5/profile_local_python_package/profile_local/src/profiles_local.py#L112C9-L112C28  # noqa
# TODO It is not recommended to use Magic Numbers in the code; it will not work on a brand-new database or another environment, and we can't use existing data that is not is_test_data  # noqa

TEST_PROFILE_ID1 = 5000002
TEST_PROFILE_ID2 = 50000392
TEST_PROFILE_ID3 = 50000409
TEST_PROFILE_ID4 = 50000405
# TODO SystemsLocals.get_test_system_id() like we do in places like ContactsLocal.get_test_contact_id()
TEST_SYSTEM_ID = 1

# TODO Uncomment the bellow code to delete the TEMP_DATA
#@pytest.fixture(scope="session")
#def setup_teardown_session():
    #print("\nSetting up for the entire test session...")
    #yield
    #call delete temp data method
    #print("\nCleaning up after the entire test session...")

user_externals_local = UserExternalsLocal(is_test_data=True)

# TODO Add setup_teardown_session as parameter to all def test*(setup_teardown_session) methods
def test_insert_get():
    # TODO test_user_external = UserExternalsLocal.get_test_user_external(__name__) like we should have  ContactsLocal.get_test_contact(__name__)
    test_username = "test_username test_insert_get " + str(datetime.now())
    access_token_test = (
        "access_token_test from test_insert_get() of test_user_externals_local.py"
    )

    user_externals_local.insert_or_update_user_external_access_token(
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
        username=test_username,
        access_token=access_token_test,
    )
    token = user_externals_local.get_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
    )
    assert token == access_token_test


def test_update_access_token():
    test_username = "test_username test_update_access_token " + str(datetime.now())
    inseted_access_token_test = "inseted_access_token_test from test_update_access_token() of test_user_externals_local.py"

    inserted_id = user_externals_local.insert_or_update_user_external_access_token(
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
        username=test_username,
        access_token=inseted_access_token_test,
    )

    updated_access_token_test = "updated_access_token_test from test_update_access_token() of test_user_externals_local.py"
    updated_id = user_externals_local.update_user_external_access_token(
        user_external_id=inserted_id,
        username=test_username,
        system_id=TEST_SYSTEM_ID,
        profile_id=TEST_PROFILE_ID1,
        access_token=updated_access_token_test,
    )

    token = user_externals_local.get_access_token(
        user_external_id=updated_id,
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
    )
    assert token == updated_access_token_test


def test_delete_access_token():
    test_username = "test_username test_delete_access_token " + str(datetime.now())

    user_externals_local.delete_access_token(
        username=test_username,
        system_id=TEST_SYSTEM_ID,
        profile_id=TEST_PROFILE_ID1,
    )

    sleep(2)

    token = user_externals_local.get_access_token(
        username=test_username,
        system_id=TEST_SYSTEM_ID,
        profile_id=TEST_PROFILE_ID1,
    )
    assert token is None


def test_insert_by_system_and_profile_get():
    # TODO test_user_external = UserExternalsLocal.get_test_user_external(__name__)
    test_username = "test_username test_insert_by_system_and_profile_get " + str(
        datetime.now()
    )

    access_token_test = "access_token_test test_insert_by_system_and_profile_get"
    refresh_token_test = "refresh_test test_insert_by_system_..."
    expiry_test = "expiry_test test_insert_by_system_..."

    user_externals_local.insert_or_update_user_external_access_token(
        profile_id=TEST_PROFILE_ID2,
        system_id=TEST_SYSTEM_ID,
        username=test_username,
        refresh_token=refresh_token_test,
        access_token=access_token_test,
        expiry=expiry_test,
    )

    auth_details = user_externals_local.get_auth_details_by_system_id_and_profile_id(
        profile_id=TEST_PROFILE_ID2, system_id=TEST_SYSTEM_ID
    )

    assert auth_details is not None
    assert auth_details.get("access_token") == access_token_test
    assert auth_details.get("refresh_token") == refresh_token_test
    assert auth_details.get("expiry") == expiry_test


def test_get_access_token_by_username_and_system_id():
    test_username = (
        "test_username test_get_access_token_by_username_and_system_id "
        + str(datetime.now())
    )
    access_token_test = (
        "access_token_test test_get_access_token_by_username_and_system_id"
    )

    user_externals_local.insert_or_update_user_external_access_token(
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
        username=test_username,
        access_token=access_token_test,
    )

    token = user_externals_local.get_access_token_by_username_and_system_id(
        username=test_username, system_id=TEST_SYSTEM_ID
    )

    assert token == access_token_test


# Tests for the deprecated static methods
def test_insert_get_static():
    logger.start("test started")
    test_username = "test_username test_insert_get_static " + str(datetime.now())
    access_token_test = "access_token_test"
    user_externals_local.insert_or_update_user_external_access_token(
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
        username=test_username,
        access_token=access_token_test,
    )
    token = user_externals_local.get_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
    )
    assert token == access_token_test
    logger.end("test successfull")


def test_update_access_token_static():
    logger.start("test started")
    test_username = "test_username test_update_access_token_static " + str(
        datetime.now()
    )
    new_access_token_test = "new_access_token_test"

    inserted_user_externals_id = (
        user_externals_local.insert_or_update_user_external_access_token(
            profile_id=TEST_PROFILE_ID1,
            system_id=TEST_SYSTEM_ID,
            username=test_username,
            access_token=new_access_token_test,
        )
    )

    user_externals_local.update_user_external_access_token(
        user_external_id=inserted_user_externals_id,
        username=test_username,
        system_id=TEST_SYSTEM_ID,
        profile_id=TEST_PROFILE_ID1,
        access_token=new_access_token_test,
    )

    token = user_externals_local.get_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
    )

    assert token == new_access_token_test
    logger.end("test successfull")


def test_delete_access_token_static():
    logger.start("test started")
    test_username = "test_username test_delete_access_token_static " + str(
        datetime.now()
    )
    access_token_test = "access_token_test test_delete_access_token_static"

    user_externals_local.insert_or_update_user_external_access_token(
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
        username=test_username,
        access_token=access_token_test,
    )

    user_externals_local.delete_access_token(
        username=test_username,
        system_id=TEST_SYSTEM_ID,
        profile_id=TEST_PROFILE_ID1,
    )

    sleep(2)

    token = user_externals_local.get_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
    )

    assert token is None
    logger.end("test successfull")


def test_insert_by_system_and_profile_get_static():
    logger.start("test started")

    test_username = "test_username test_insert_by_system_and_profile_get_static " + str(
        datetime.now()
    )

    access_token_test = "access_token_test test_insert_by_system_and_profile_get_static"
    expiry_test = "expiry_test test_insert_by_system_an..."
    refresh_token_test = "refresh_test test_insert_by_system_..."

    user_externals_local.insert_or_update_user_external_access_token(
        profile_id=TEST_PROFILE_ID2,
        system_id=TEST_SYSTEM_ID,
        username=test_username,
        refresh_token=refresh_token_test,
        access_token=access_token_test,
        expiry=expiry_test,
    )

    auth_details = user_externals_local.get_auth_details_by_system_id_and_profile_id(
        profile_id=TEST_PROFILE_ID2, system_id=TEST_SYSTEM_ID
    )

    assert auth_details is not None
    assert auth_details.get("access_token") == access_token_test
    assert auth_details.get("refresh_token") == refresh_token_test
    assert auth_details.get("expiry") == expiry_test

    logger.end("test successfull")


def test_get_access_token_by_username_and_system_id_static():
    logger.start()
    test_username = (
        "test_username test_get_access_token_by_username_and_system_id_static "
        + str(datetime.now())
    )
    access_token_test = (
        "access_token_test test_get_access_token_by_username_and_system_id_static"
    )

    user_externals_local.insert_or_update_user_external_access_token(
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
        username=test_username,
        access_token=access_token_test,
    )

    access_token = user_externals_local.get_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
    )

    assert access_token == access_token_test
    logger.end("test successfull")


def test_get_credentials_storage_id_by_system_id_and_profile_id():
    test_credentials_storage_id = (
        user_externals_local.get_credential_storage_id_by_system_id_and_profile_id(
            profile_id=TEST_PROFILE_ID1, system_id=TEST_SYSTEM_ID
        )
    )
    correct_credential_storage_id = user_externals_local.select_one_value_by_where(
        schema_name=user_externals_local.default_schema_name,
        view_table_name=user_externals_local.default_view_table_name,
        select_clause_value="credential_storage_id",
        where=f"system_id={TEST_SYSTEM_ID} AND username={TEST_PROFILE_ID1}",
    )
    assert test_credentials_storage_id == correct_credential_storage_id
    logger.end()


def test_cannot_insert_two_identical_entries():
    data_to_insert = {
        # TODO Replace Magic Numbers such as 1 with enum/const from python-sdk-remote system.py
        "system_id":1,
        "main_profile_id":5000002,
        "username":"test_username_"+str(datetime.now()),
        "is_test_data":True,
    }
    user_externals_local.insert(
        data_dict=data_to_insert,
    )
    try:
        user_externals_local.insert(
            data_dict=data_to_insert,
        )
        assert False
    except mysql.connector.errors.IntegrityError:
        assert True
    

def test_get_password_clear_text_by_system_id_and_profile_id():
    test_username = (
        "test_username " + str(datetime.now())
    )
    access_token_test = (
        "access_token_test"
    )

    test_password = "test_password_clear_text"

    # uncomment and update the TEST_PROFILE_ID_4 if you want to insert a new entry
    
    # TODO How can we make it work in all databases / empty databases? Can we enhance ProfilesLocal.get_test_profile_id() so we can use it?

    # user_externals_local.insert_or_update_user_external_access_token(
    #     username=test_username,
    #     profile_id=TEST_PROFILE_ID3,
    #     system_id=TEST_SYSTEM_ID,
    #     access_token=access_token_test,
    # )
    # test_user_external_id = user_externals_local.get_user_external_id_by_profile_id_system_id_username(
    #     profile_id=TEST_PROFILE_ID3,
    #     system_id=TEST_SYSTEM_ID,
    #     username=test_username
    #     )

    # test_data = {"password_clear_text": test_password, "user_external_id": test_user_external_id}

    # user_externals_local.user_external_pii.insert(
    #     data_dict=test_data,
    #     commit_changes=True,
    # )
    # user_externals_local.connection.commit()

    assert test_password == user_externals_local.get_password_clear_text_by_system_id_and_profile_id(system_id=TEST_SYSTEM_ID, profile_id=TEST_PROFILE_ID3)

    try:
        user_externals_local.get_password_clear_text_by_system_id_and_profile_id(system_id=TEST_SYSTEM_ID, profile_id=TEST_PROFILE_ID4)
        assert False
    except mysql.connector.errors.InternalError:
        assert True

def test_get_password_clear_text_by_system_id_profile_id_username():
    test_username = (
        "test_username " + str(datetime.now())
    )
    access_token_test = (
        "access_token_test"
    )

    user_externals_local.insert_or_update_user_external_access_token(
        username=test_username,
        profile_id=TEST_PROFILE_ID2,
        system_id=TEST_SYSTEM_ID,
        access_token=access_token_test,
    )
    test_user_external_id = user_externals_local.get_user_external_id_by_profile_id_system_id_username(
        profile_id=TEST_PROFILE_ID2,
        system_id=TEST_SYSTEM_ID,
        username=test_username
        )
    
    test_password = "test_password_clear_text"

    test_data = {"password_clear_text": test_password, "user_external_id": test_user_external_id}

    user_externals_local.user_external_pii.insert(
        data_dict=test_data,
        commit_changes=True,
    )
    user_externals_local.connection.commit()

    assert test_password == user_externals_local.get_password_clear_text_by_system_id_profile_id_username(system_id=TEST_SYSTEM_ID, 
                                                                                                          profile_id=TEST_PROFILE_ID2, 
                                                                                                          username=test_username
                                                                                                          )
    
def test_get_multiple_user_external_ids_by_profile_id_system_id():
    TEST_PROFILE_ID1 = 5000002
    TEST_SYSTEM_ID = 1

    #Create 3 usernames externals with the same profile_id and system_id
    test_username_1 = f"test_username_1_{datetime.now().isoformat()}"
    test_username_2 = f"test_username_2_{datetime.now().isoformat()}"
    test_username_3 = f"test_username_3_{datetime.now().isoformat()}"

    user_dict_1 = {
        "system_id": TEST_SYSTEM_ID,
        "main_profile_id": TEST_PROFILE_ID1,
        "username": test_username_1,
        "is_test_data": True,
    }
    user_external_id_1 = user_externals_local.insert(
        data_dict=user_dict_1,
    )

    user_dict_2 = {
        "system_id": TEST_SYSTEM_ID,
        "main_profile_id": TEST_PROFILE_ID1,
        "username": test_username_2,
        "is_test_data": True,
    }
    user_external_id_2 = user_externals_local.insert(
        data_dict=user_dict_2,
    )   

    user_dict_3 = {
        "system_id": TEST_SYSTEM_ID,
        "main_profile_id": TEST_PROFILE_ID1,
        "username": test_username_3,
        "is_test_data": True,
    }
    user_external_id_3 = user_externals_local.insert(
        data_dict=user_dict_3,
    )

    # Get the user external ids by profile_id and system_id
    user_external_ids = user_externals_local._UserExternalsLocal__get_user_external_ids_by_profile_id_system_id(
        profile_id=TEST_PROFILE_ID1,
        system_id=TEST_SYSTEM_ID,
    )
    # Check if the returned ids match the inserted ones
    assert len(user_external_ids) >= 3


    assert user_external_id_1 in user_external_ids
    assert user_external_id_2 in user_external_ids
    assert user_external_id_3 in user_external_ids
    

# TODO Shall we add another method to get the email_address: EmailAddress class and not email_address_id?
def test_get_email_address_id_by_user_external_id():
    data_to_insert = {
        # TODO Replace Magic Numbers such as 1 with enum/const from python-sdk-remote system.py
        "system_id":1,
        "main_profile_id":5000002,
        "username":"test_username_"+str(datetime.now()),
        # TODO Replace the True with GenericCrud.TEMP_DATA
        "is_test_data": True,
        "email_address_id":78,
    }
    user_external_id = user_externals_local.insert(
        data_dict=data_to_insert,
    )
    assert user_externals_local.get_email_address_id_by_user_external_id(user_external_id=user_external_id) == 78





# to use to debugge the code, uncomment the test function you want to run
if __name__ == "__main__":
    # test_insert_get()
    test_update_access_token()
    # test_delete_access_token()
    # test_insert_by_system_and_profile_get()
    # test_get_access_token_by_username_and_system_id()
    # test_insert_get_static()
    # test_update_access_token_static()
    # test_delete_access_token_static()
    # test_insert_by_system_and_profile_get_static()
    # test_get_access_token_by_username_and_system_id_static()
    # test_get_credentials_storage_id_by_system_id_and_profile_id()
