# When testing using .env file, uncomment below
from dotenv import load_dotenv
import random
from datetime import datetime
from time import sleep
from logger_local.LoggerComponentEnum import LoggerComponentEnum
from logger_local.LoggerLocal import Logger

from src.token__user_external import (
    TokenUserExternals,
    USER_EXTERNAL_LOCAL_PYTHON_PACKAGE_COMPONENT_ID,
    USER_EXTERNAL_LOCAL_PYTHON_PACKAGE_COMPONENT_NAME,
    DEVELOPER_EMAIL,
)
from src.user_externals_local import UserExternalsLocal

load_dotenv()

TEST_USERNAME = "TEST_USERNAME" + str(datetime.now())
TEST_TOKEN_NAME = "TEST_TOKEN" + str(datetime.now())
TEST_STATIC_TOKEN_NAME = "TEST_STATIC_TOKEN" + str(datetime.now())
object_init = {
    "component_id": USER_EXTERNAL_LOCAL_PYTHON_PACKAGE_COMPONENT_ID,
    "component_name": USER_EXTERNAL_LOCAL_PYTHON_PACKAGE_COMPONENT_NAME,
    "component_category": LoggerComponentEnum.ComponentCategory.Unit_Test.value,
    "developer_email": DEVELOPER_EMAIL,
    "testing_framework": LoggerComponentEnum.testingFramework.pytest.value,
}
logger = Logger.create_logger(object=object_init)

# Test user external ID to use - we'll create in setup
TEST_SYSTEM_ID = 1
TEST_PROFILE_ID = 5000002

# Initialize our classes for testing
token_user_externals = TokenUserExternals(is_test_data=True)
user_externals_local = UserExternalsLocal(is_test_data=True)


def test_insert_token():
    logger.info("""Test inserting a new token""")
    test_username = TEST_USERNAME + "_insert"
    access_token_test = "access_token_test test_insert_token " + str(datetime.now())

    user_external_inserted_id = (
        user_externals_local.insert_or_update_user_external_access_token(
            system_id=TEST_SYSTEM_ID,
            username=test_username,
            access_token=access_token_test,
            profile_id=TEST_PROFILE_ID,
        )
    )

    new_access_token_test = "new_access_token_test " + str(datetime.now())

    token_user_externals.insert_or_update_user_external_access_token(
        user_external_id=user_external_inserted_id,
        profile_id=TEST_PROFILE_ID,
        username=TEST_USERNAME,
        access_token=new_access_token_test,
    )

    token = token_user_externals.get_access_token(
        user_external_id=user_external_inserted_id,
    )

    assert token == new_access_token_test


def test_update_token():
    logger.info("""Test updating an existing token""")
    test_username_update = TEST_USERNAME + "_update"

    old_access_token_test = "old_access_token_test from test_update_token"
    new_access_token_test = "new_access_token_test from test_update_token"

    user_external_id = user_externals_local.insert_or_update_user_external_access_token(
        system_id=TEST_SYSTEM_ID,
        username=test_username_update,
        access_token=old_access_token_test,
        profile_id=TEST_PROFILE_ID,
    )

    token_user_externals.update_access_token(
        user_external_id=user_external_id,
        name=TEST_USERNAME,
        access_token=new_access_token_test,
    )

    token = token_user_externals.get_access_token(
        user_external_id=user_external_id,
    )

    assert token == new_access_token_test


def test_delete_token():
    logger.info("""Test deleting a token""")

    test_username = TEST_USERNAME + "_delete"
    to_delete_access_token_test = "to_delete_access_token_test " + str(datetime.now())

    user_external_id = user_externals_local.insert_or_update_user_external_access_token(
        system_id=TEST_SYSTEM_ID,
        username=test_username,
        access_token=to_delete_access_token_test,
        profile_id=TEST_PROFILE_ID,
    )

    deleted_rows = token_user_externals.delete_access_token_by_user_external_id(
        user_external_id=user_external_id,
        username=TEST_USERNAME,
    )

    sleep(2)  # Wait for deletion to process

    access_token = token_user_externals.get_access_token(
        user_external_id=user_external_id,
    )

    assert access_token is None, "Token was not deleted successfully"
    assert deleted_rows == 1, "Failed to delete the token"


def test_insert_with_expiry_and_refresh():
    logger.info("""Test inserting a token with expiry and refresh token values""")

    test_username = TEST_USERNAME + "_expiry"
    access_token_test = "access_token_with_expiry" + str(datetime.now())
    expiry_test = "test_expiry_date" + str(datetime.now())
    refresh_token_test = "test_refresh_token" + str(datetime.now())

    user_externals_id_with_expiry = (
        user_externals_local.insert_or_update_user_external_access_token(
            system_id=TEST_SYSTEM_ID,
            profile_id=TEST_PROFILE_ID,
            username=test_username,
            access_token=access_token_test,
            expiry=expiry_test,
            refresh_token=refresh_token_test,
        )
    )

    auth_details = token_user_externals.get_auth_details(
        user_external_id=user_externals_id_with_expiry,
    )

    assert auth_details is not None, "Auth details should not be None"
    assert (
        auth_details.get("access_token") == access_token_test
    ), "Access token mismatch"
    assert (
        auth_details.get("refresh_token") == refresh_token_test
    ), "Refresh token mismatch"
    assert auth_details.get("expiry") == expiry_test, "Expiry date mismatch"


def test_update_with_expiry_and_refresh():
    logger.info("""Test updating a token with expiry and refresh token values""")

    test_username = TEST_USERNAME + "_expiry_refresh"
    old_access_token_test = "old_access_token test_update_with_expiry_and_refresh"
    old_expiry_test = "old_expiry_date"

    new_access_token_test = "new_access_token test_update_with_expiry_and_refresh"
    new_expiry_test = "new_expiry_date" + str(datetime.now())

    user_external_id = user_externals_local.insert_or_update_user_external_access_token(
        profile_id=TEST_PROFILE_ID,
        username=test_username,
        system_id=TEST_SYSTEM_ID,
        access_token=old_access_token_test,
        expiry=old_expiry_test,
    )

    token_user_externals.update_access_token(
        user_external_id=user_external_id,
        name=test_username,
        access_token=new_access_token_test,
        expiry=new_expiry_test,
    )

    auth_details = token_user_externals.get_auth_details(
        user_external_id=user_external_id,
    )

    assert auth_details is not None, "Auth details should not be None"
    assert (
        auth_details.get("access_token") != old_access_token_test
    ), "Access token was not updated"
    assert auth_details.get("expiry") != old_expiry_test, "Expiry date was not updated"

    assert (
        auth_details.get("access_token") == new_access_token_test
    ), "Access token was not updated correctly"
    assert (
        auth_details.get("expiry") == new_expiry_test
    ), "Expiry date was not updated correctly"


def test_get_tokens_by_user_external_id():
    logger.info("""Test getting all tokens for a specific user external ID""")

    usernames = [f"{TEST_USERNAME}_{i}" for i in range(3)]
    access_tokens = [f"access_token_test {i}" for i in range(3)]
    inserted_ids = []

    for i, token_name in enumerate(usernames):
        inserted_id = user_externals_local.insert_or_update_user_external_access_token(
            profile_id=TEST_PROFILE_ID,
            username=token_name,
            access_token=access_tokens[i],
            system_id=TEST_SYSTEM_ID,
        )
        inserted_ids.append(inserted_id)

    token1_from_username_and_system_id = (
        token_user_externals.get_access_token_by_username_and_system_id(
            username=usernames[0],
            system_id=TEST_SYSTEM_ID,
        )
    )
    token2_from_username_and_system_id = (
        token_user_externals.get_access_token_by_username_and_system_id(
            username=usernames[1],
            system_id=TEST_SYSTEM_ID,
        )
    )
    token3_from_username_and_system_id = (
        token_user_externals.get_access_token_by_username_and_system_id(
            username=usernames[2],
            system_id=TEST_SYSTEM_ID,
        )
    )

    token1_from_user_external_id = token_user_externals.get_access_token(
        user_external_id=inserted_ids[0],
    )
    token2_from_user_external_id = token_user_externals.get_access_token(
        user_external_id=inserted_ids[1],
    )
    token3_from_user_external_id = token_user_externals.get_access_token(
        user_external_id=inserted_ids[2],
    )

    assert token1_from_username_and_system_id == access_tokens[0], "Token 1 mismatch"
    assert token2_from_username_and_system_id == access_tokens[1], "Token 2 mismatch"
    assert token3_from_username_and_system_id == access_tokens[2], "Token 3 mismatch"

    assert token1_from_user_external_id == access_tokens[0], "Token 1 mismatch"
    assert token2_from_user_external_id == access_tokens[1], "Token 2 mismatch"
    assert token3_from_user_external_id == access_tokens[2], "Token 3 mismatch"

    assert (
        token1_from_user_external_id == token1_from_username_and_system_id
    ), "Token 1 mismatch"
    assert (
        token2_from_user_external_id == token2_from_username_and_system_id
    ), "Token 2 mismatch"
    assert (
        token3_from_user_external_id == token3_from_username_and_system_id
    ), "Token 3 mismatch"


def test_duplicate_insert_handles_integrity_error():
    logger.info(
        """Test that inserting a duplicate token throws IntegrityError but recovers"""
    )
    duplicate_test_username = TEST_USERNAME + "_duplicate"
    access_token_test = (
        "duplicate_token_test of test_duplicate_insert_handles_integrity_error"
    )

    # Insert the first token
    user_external_inserted_id = (
        user_externals_local.insert_or_update_user_external_access_token(
            profile_id=TEST_PROFILE_ID,
            username=duplicate_test_username,
            access_token=access_token_test,
            system_id=TEST_SYSTEM_ID,
        )
    )

    # Insert the same token again - this should update instead
    updated_token = "updated_duplicate_token"
    user_external_updated_id = (
        token_user_externals.insert_or_update_user_external_access_token(
            user_external_id=user_external_inserted_id,
            profile_id=TEST_PROFILE_ID,
            username=duplicate_test_username,
            access_token=updated_token,
        )
    )

    assert (
        user_external_updated_id == user_external_inserted_id
    ), "User external ID mismatch after update"

    token = token_user_externals.get_access_token(
        user_external_id=user_external_inserted_id,
    )

    assert token == updated_token


def test_get_nonexistent_token():
    logger.info("""Test getting a token that doesn't exist""")
    token = token_user_externals.get_access_token(
        user_external_id=random.randint(1, 1000000),  # Random ID that doesn't exist
    )

    assert token is None


def test_get_auth_details_nonexistent():
    logger.info("""Test getting auth details for a token that doesn't exist""")
    auth_details = token_user_externals.get_auth_details(
        user_external_id=random.randint(1, 1000000),  # Random ID that doesn't exist
    )

    assert auth_details is None


# To use to debug the code, uncomment the test function you want to run
if __name__ == "__main__":
    # test_insert_token()
    # test_update_token()
    # test_delete_token()
    test_insert_with_expiry_and_refresh()
    # test_update_with_expiry_and_refresh()
    # test_get_tokens_by_user_external_id()
    # test_duplicate_insert_handles_integrity_error()
    # test_get_nonexistent_token()
    # test_get_auth_details_nonexistent()
