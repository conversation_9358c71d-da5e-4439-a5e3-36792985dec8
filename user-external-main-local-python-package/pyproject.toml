# This file should be in the future instead of setup.py
# https://python-poetry.org/docs/pyproject

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.poetry]
name = "user-external-local"
version = "0.0.46" # https://pypi.org/project/user-external-local
description = "external user Python Package"
readme = "README.md"
authors = [
    "Circlez.ai <<EMAIL>>",
]