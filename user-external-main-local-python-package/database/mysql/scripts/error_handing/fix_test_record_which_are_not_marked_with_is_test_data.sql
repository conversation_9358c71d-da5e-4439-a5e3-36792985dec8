UPDATE user_external.user_external_table
SET is_test_data= TRUE
WHERE is_test_data IS NULL
  AND (access_token = "new_access_token_test" OR access_token = "access_token_test" OR access_token = "token1" OR
       access_token = "sample_token" OR access_token = "access_token" OR access_token LIKE "test_access_token%" OR
       access_token LIKE "%aaaa%" OR access_token = "acceeesss" OR access_token IS NULL);
