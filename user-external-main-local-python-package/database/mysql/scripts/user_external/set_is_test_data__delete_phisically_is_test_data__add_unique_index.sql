SELECT user_extenal_id, system_id, username, end_timestamp
  FROM user_external.user_external_table
    WHERE username="<EMAIL>" 
    -- AND end_timestamp IS NULL
  ;

-- Review problematic records as we don't have unique index
SELECT user_external_id, end_timestamp, count(*)
  FROM user_external.user_external_table
  WHERE end_timestamp IS NOT NULL
  GROUP BY end_timestamp
  HAVING count(*)>1
;

-- Mark all records with same end_timestamp as is_test_data
UPDATE user_external.user_external_table
SET is_test_data=true
WHERE user_external_id IN 
(SELECT user_external_id FROM
(SELECT user_external_id
  FROM user_external.user_external_table
  WHERE end_timestamp IS NOT NULL AND is_test_data IS NOT TRUE
  GROUP BY system_id, end_timestamp
  HAVING count(*)>1) AS aaaa)
;

-- Not needed
-- We prefer not <NAME_EMAIL> user external
-- UPDATE user_external.user_external_table
--   SET is_test_data=TRUE
--   WHERE is_test_data IS NULL
--     AND username="<EMAIL>" 
--   -- AND end_timestamp IS NULL
--   ;

-- user_external which is not is_test_data
SELECT system_id, user_external_id, username, end_timestamp, is_test_data
  FROM user_external.user_external_table
  WHERE is_test_data IS NOT TRUE
  ORDER BY system_id, username, created_timestamp;

-- Review user_external without username
SELECT user_external_id, system_id, email_address_id, username, handle, main_profile_id, end_timestamp, is_test_data
  FROM user_external.user_external_table
  WHERE username IS NULL
    AND is_test_data IS NOT TRUE;

-- Mark all user_external without username as is_test_data=true
UPDATE user_external.user_external_table
SET is_test_data=true
WHERE user_external_id IN 
(SELECT user_external_id FROM
(SELECT user_external_id, system_id, email_address_id, username, handle, main_profile_id, end_timestamp, is_test_data
  FROM user_external.user_external_table
  WHERE username IS NULL
    AND is_test_data IS NOT TRUE) AS aaa);

UPDATE user_external.user_external_table
SET is_test_data=true
WHERE username LIKE "TEST%";


-- Check before delete
SELECT *
FROM user_external.user_external_table
WHERE is_test_data IS TRUE;

SET FOREIGN_KEY_CHECKS = 0;
DELETE FROM user_external.user_external_table
WHERE is_test_data IS TRUE;
SET FOREIGN_KEY_CHECKS = 1;
-- Delete Phisically all is_test_data records using Python program as we can't have end_timestamp with same value
-- Other option is to set random end_timestamp

-- We might need to delete the orphan token__user_external after deleting the user_external

ALTER TABLE `user_external`.`user_external_table` 
ADD UNIQUE INDEX `user_external.system_id.username.end_timestamp.uniqie` (`system_id` ASC, `end_timestamp` ASC, `username` ASC) VISIBLE;
;
 
