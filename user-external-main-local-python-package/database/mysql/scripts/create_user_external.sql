SELECT * FROM user_external.user_external_table
WHERE username LIKE "+13109469744";

-- Add WhatsApp user_external
INSERT INTO `user_external`.`user_external_table` (`number`, `identifier`, `system_id`, `username`) VALUES ('13109469744', '13109469744', '18', '+13109469744');

-- Facebook
INSERT INTO `user_external`.`user_external_table` (`system_id`, `username`) VALUES ('2', '<EMAIL>');
SELECT * FROM user_external.user_external_table WHERE username LIKE "%david%";

INSERT INTO `user_external_pii`.`user_external_pii_table` (`user_external_piii_id`, `user_external_id`, `password_clear_text`, `is_test_data`, `created_user_id`, `created_real_user_id`, `created_effective_user_id`, `created_effective_profile_id`, `updated_user_id`, `updated_real_user_id`, `updated_effective_user_id`, `updated_effective_profile_id`) VALUES ('2190', '2190', 'Dav1Wex2!$', '0', '1', '1', '1', '1', '1', '1', '1', '1');

INSERT INTO `profile_user_external`.`profile_user_external_table` (`user_external_id`, `profile_id`, `created_user_id`, `created_real_user_id`, `created_effective_user_id`, `created_effective_profile_id`, `updated_user_id`, `updated_real_user_id`, `updated_effective_user_id`, `updated_effective_profile_id`, `is_test_data`) VALUES ('2190', '101011007', '1', '1', '1', '1', '1', '1', '1', '1', '0');
