-- delete orphan records in token__user_external_table which do not exists in user_external_table

-- Disable foreign key checks
SET FOREIGN_KEY_CHECKS = 0;

-- Delete orphaned records in token__user_external_table
DELETE FROM user_external.token__user_external_table
WHERE user_external_id NOT IN (
    SELECT user_external_id 
    FROM user_external.user_external_table
);

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;