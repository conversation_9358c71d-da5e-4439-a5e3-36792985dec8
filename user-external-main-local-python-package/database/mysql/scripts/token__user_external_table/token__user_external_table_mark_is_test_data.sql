SELECT *
FROM user_external.token__user_external_table token__user_external
WHERE access_token="access_token_test"
  OR access_token="updated_duplicate_token"
  OR is_test_data IS TRUE;
  
UPDATE user_external.token__user_external_table token__user_external
SET is_test_data=TRUE
WHERE access_token="access_token_test"
  OR access_token="updated_duplicate_token"
  OR access_token="test_setup_token"
  OR access_token LIKE "test_%"
  OR name LIKE "TEST_%"
  OR name LIKE "test_user_"
  ;


DELETE
FROM user_external.token__user_external_table token__user_external
WHERE is_test_data=TRUE
  OR access_token="access_token_test"
  OR access_token="updated_duplicate_token";