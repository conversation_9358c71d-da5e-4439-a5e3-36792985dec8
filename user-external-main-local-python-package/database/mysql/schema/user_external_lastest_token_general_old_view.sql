-- View for backward compatibility
USE user_external;
CREATE OR REPLACE
VIEW `user_external`.`user_external_latest_token_general_old_view` AS
    SELECT 
        `user_external`.`user_external_id` AS `user_external_id`,
        `user_external`.`system_id` AS `system_id`,
        `user_external`.`subsystem_id` AS `subsystem_id`,
        `user_external`.`username` AS `username`,
        `user_external`.`refresh_token_old` AS `refresh_token`,
	user_external.is_refresh_token_valid,
        `user_external`.`is_test_data` AS `user_external.is_test_data`,
        `token__user_external`.`name` AS `name`,
        `token__user_external`.`description` AS `description`,
        `token__user_external`.`end_timestamp` AS `token__user_external.end_timestamp`,
        `token__user_external`.`access_token` AS `access_token`,
        `token__user_external`.`base_credential_token` AS `base_credential_token`,
        `token__user_external`.`oauth_state` AS `oauth_state`,
        `token__user_external`.`expiry` AS `expiry`,
        `token__user_external`.`updated_timestamp` AS `updated_timestamp`,
        `token__user_external`.`is_test_data` AS `token__user_external.is_test_data`,
        `profile_user_external`.`profile_user_external_id` AS `profile_user_external_id`,
        `profile_user_external`.`profile_id` AS `profile_id`,
        `profile_user_external`.`is_test_data` AS `profile_user_external.is_test_data`,
        `profile_user_external`.`end_timestamp` AS `end_timestamp`
    FROM user_external_table user_external
	LEFT JOIN `user_external`.`token__user_external_old_table` `token__user_external`
	  ON user_external.user_external_id=`token__user_external`.user_externaluser_external_latest_token_general_old_view_id AND token__user_external.end_timestamp IS NULL 
	LEFT JOIN profile_user_external.profile_user_external_table profile_user_external
      ON profile_user_external.user_external_id=user_external.user_external_id AND profile_user_external.end_timestamp IS NULL 
	WHERE user_external.end_timestamp IS NULL
      AND token__user_external.token__user_external_id = (SELECT token__user_external_id
      FROM token__user_external_old_table
      WHERE user_external.user_external_id=token__user_external_old_table.user_external_id
      ORDER BY updated_timestamp DESC
      LIMIT 1
	)
