-- Active: 1732024539973@@mysql-db.dvlp1.circ.zone@3306
USE user_external;

CREATE
OR REPLACE VIEW `user_external`.`user_external_latest_token_general_view` AS
SELECT
  `user_external`.`user_external_id` AS `user_external_id`,
  `user_external`.`system_id` AS `system_id`,
  `user_external`.`subsystem_id` AS `subsystem_id`,
  `user_external`.`username` AS `username`,
  `project__user_external`.`refresh_token` AS `refresh_token`,
  user_external.is_refresh_token_valid,
  `user_external`.`is_test_data` AS `user_external.is_test_data`,
  `project__user_external`.`name` AS `project__user_external.name`,
  `project__user_external`.`description` AS `project__user_external.description`,
  `project_user_external__access_token`.`end_timestamp` AS `project_user_external__access_token.end_timestamp`,
  `project_user_external__access_token`.`access_token` AS `access_token`,
  `project_user_external__access_token`.`base_credential_token` AS `base_credential_token`,
  `project_user_external__access_token`.`oauth_state` AS `oauth_state`,
  `project_user_external__access_token`.`expiry` AS `expiry`,
  `project_user_external__access_token`.`updated_timestamp` AS `updated_timestamp`,
  `project_user_external__access_token`.`is_test_data` AS `project_user_external__access_token.is_test_data`,
  `profile_user_external`.`profile_user_external_id` AS `profile_user_external_id`,
  `profile_user_external`.`profile_id` AS `profile_id`,
  `profile_user_external`.`is_test_data` AS `profile_user_external.is_test_data`,
  `profile_user_external`.`end_timestamp` AS `end_timestamp`
FROM
  user_external_table user_external
  LEFT JOIN `user_external`.`project__user_external_table` `project__user_external` ON user_external.user_external_id = `project__user_external`.user_external_id
  AND project__user_external.end_timestamp IS NULL
  LEFT JOIN `user_external`.`project_user_external__access_token_table` `project_user_external__access_token` ON project_user_external__access_token.project__user_external_id = `project__user_external`.project__user_external_id
  AND project__user_external.end_timestamp IS NULL
  LEFT JOIN profile_user_external.profile_user_external_table profile_user_external ON profile_user_external.user_external_id = user_external.user_external_id
  AND profile_user_external.end_timestamp IS NULL
WHERE
  user_external.end_timestamp IS NULL
  AND project_user_external__access_token.project_user_external__access_token_id = (
    SELECT
      project_user_external__access_token_id
    FROM
      project_user_external__access_token_table
    WHERE
      project_user_external__access_token_table.project__user_external_id = project__user_external.project__user_external_id
    ORDER BY
      updated_timestamp DESC
    LIMIT
      1
  )