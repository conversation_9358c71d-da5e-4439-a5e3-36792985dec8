CREATE 
    ALGORITHM = UNDEFINED 
    DEFINER = `bubbelz`@`%` 
    SQL SECURITY DEFINER
VIEW `user_external_pii`.`user_external_pii_general_view` AS
    SELECT 
        `user_external`.`user_external_id` AS `user_external_id`,
        `user_external`.`system_id` AS `system_id`,
        `user_external`.`username` AS `username`,
        `user_external_pii`.`user_external_pii`.`password_clear_text` AS `password_clear_text`
    FROM
        (`user_external`.`user_external_table` `user_external`
        LEFT JOIN `user_external_pii`.`user_external_pii_view` `user_external_pii` ON ((`user_external_pii`.`user_external_pii`.`user_external_id` = `user_external`.`user_external_id`)))
    WHERE
        ((0 <> `user_external`.`is_test_data`)
            IS NOT TRUE)
