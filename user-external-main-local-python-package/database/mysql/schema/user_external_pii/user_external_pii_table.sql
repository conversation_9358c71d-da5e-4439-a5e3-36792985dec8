CREATE SCHEMA user_external_pii;
USE user_external_pii;
CREATE TABLE user_external_pii.`user_external_pii_table` (
  `user_external_pii_id` bigint unsigned NOT NULL,
  `number` bigint unsigned DEFAULT NULL,
  `identifier` varchar(255) DEFAULT NULL,
  `user_external_id` bigint unsigned NOT NULL COMMENT 'PK: user_external_pii_id',
  `password_clear_text` varchar(30) NOT NULL,
  `password_encrypted` varchar(30) DEFAULT NULL,
  `is_test_data` tinyint DEFAULT NULL,
  `start_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_timestamp` timestamp NULL DEFAULT NULL,
  `created_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_user_id` bigint unsigned NOT NULL COMMENT 'TODO We need to remove this',
  `created_real_user_id` bigint unsigned NOT NULL,
  `created_effective_user_id` bigint unsigned NOT NULL,
  `created_effective_profile_id` bigint unsigned NOT NULL,
  `updated_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_user_id` bigint unsigned NOT NULL COMMENT 'TODO We need to remove this',
  `updated_real_user_id` bigint unsigned NOT NULL,
  `updated_effective_user_id` bigint unsigned NOT NULL,
  `updated_effective_profile_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`user_external_pii_id`),
  UNIQUE KEY `user_external_pii.user_external_pii_id.unique` (`user_external_pii_id`),
  CONSTRAINT `user_external_pii_table.user_extenal_id.fk` FOREIGN KEY (`user_external_id`) REFERENCES `user_external`.`user_external_table` (`user_external_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
