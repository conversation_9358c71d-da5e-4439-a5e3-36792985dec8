ALTER TABLE `user_external`.`user_external_table` 
ADD UNIQUE INDEX `user_id.system_id.main_profile_id.unique` (`system_id` ASC, `subsystem_id` ASC, `username` AS<PERSON>, `main_profile_id` ASC) VISIBLE;
;

ALTER TABLE `user_external`.`user_external_table` 
ADD UNIQUE INDEX `system_id.subsystem_id.username.end_timestamp.unique` (`system_id` ASC, `subsystem_id` ASC, `username` ASC, `end_timestamp` ASC) VISIBLE;
;

ALTER TABLE `user_external`.`user_external_table` 
ADD UNIQUE INDEX `user_id.system_id.end_timestamp.unique` (`system_id` ASC, `username` ASC, `end_timestamp` ASC) VISIBLE;
;
