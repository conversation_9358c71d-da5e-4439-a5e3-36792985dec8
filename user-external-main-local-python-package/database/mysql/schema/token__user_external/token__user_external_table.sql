CREATE TABLE `token__user_external_table` (
  `token__user_external_id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_external_id` bigint unsigned NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `description` text COMMENT 'remark',
  `end_timestamp` timestamp NULL DEFAULT NULL,
  `access_token` varchar(255) DEFAULT NULL,
  `base_credential_token` varchar(255) DEFAULT NULL COMMENT 'With username',
  `oauth_state` varchar(1024) DEFAULT NULL,
  `expiry` varchar(45) DEFAULT NULL,
  `refresh_token_old` varchar(45) DEFAULT NULL,
  `updated_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_test_data` tinyint DEFAULT NULL,
  `project` varchar(45) DEFAULT NULL,
  `scope_json` text,
  PRIMARY KEY (`token__user_external_id`),
  UNIQUE KEY `user_external_token_id_UNIQUE` (`token__user_external_id`),
  UNIQUE KEY `user_external.project.scope.end_timestamp..unique` (`user_external_id`,`project`,`scope_json`(255),`end_timestamp`),
  KEY `index2` (`user_external_id`),
  CONSTRAINT `fk_token__user_external_table_1` FOREIGN KEY (`user_external_id`) REFERENCES `user_external_table` (`user_external_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6019 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;




-- ALTER TABLE `user_external`.`token__user_external_table` 
-- ADD UNIQUE INDEX `user_external.project.scope.end_timestamp..unique` (`user_external_id` ASC, `project` ASC, `scope_json`(255) ASC, `end_timestamp` ASC) VISIBLE;
-- ;
