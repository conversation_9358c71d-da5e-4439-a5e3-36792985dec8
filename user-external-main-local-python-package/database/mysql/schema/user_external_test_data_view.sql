CREATE VIEW `user_external_test_data_view` AS
SELECT user_external_id
     , username
     , is_test_data
     , created_user_id -- OLD
     , created_real_user_id
     , created_effective_user_id
     , created_effective_profile_id
     , updated_user_id -- OLD
     , updated_real_user_id
     , updated_effective_user_id
     , updated_effective_profile_id
FROM user_external.user_external_email_account_general_view
WHERE username LIKE "TEST%"
  and is_test_data IS NOT TRUE;
