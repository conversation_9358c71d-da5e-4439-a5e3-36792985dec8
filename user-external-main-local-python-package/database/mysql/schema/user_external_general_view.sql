CREATE 
    ALGORITHM = UNDEFINED 
    DEFINER = `bubbelz`@`%` 
    SQL SECURITY DEFINER
VIEW `user_external`.`user_external_general_view` AS
    SELECT 
        `user_external`.`user_external_id` AS `user_external_id`,
        `user_external`.`number` AS `number`,
        `user_external`.`system_id` AS `system_id`,
        `user_external`.`subsystem_id` AS `subsystem_id`,
        `user_external`.`username` AS `username`,
        `user_external`.`email_address_id` AS `email_address_id`,
        `user_external`.`handle` AS `handle`,
        `user_external`.`access_token` AS `access_token`,
        `user_external`.`token_2delete` AS `token_2delete`,
        `user_external`.`refresh_token` AS `refresh_token`,
        `user_external`.`expiry` AS `expiry`,
        `user_external`.`oauth_token` AS `oauth_token`,
        `user_external`.`oauth_token_secret` AS `oauth_token_secret`,
        `user_external`.`oauth_callback_confirmed` AS `oauth_callback_confirmed`,
        `user_external`.`environment_id_old` AS `environment_id_old`,
        `user_external`.`is_test_data` AS `is_test_data`,
        `user_external`.`start_timestamp` AS `start_timestamp`,
        `user_external`.`end_timestamp` AS `end_timestamp`,
        `user_external`.`created_timestamp` AS `created_timestamp`,
        `user_external`.`created_user_id` AS `created_user_id`,
        `user_external`.`updated_timestamp` AS `updated_timestamp`,
        `user_external`.`updated_user_id` AS `updated_user_id`,
        `profile_user_external`.`profile_user_external_id` AS `profile_user_external_id`,
        `profile_user_external`.`profile_id` AS `profile_id`,
        `user`.`user_id` AS `user_id`,
        `person`.`person_id` AS `person_id`,
        `email_account`.`email_account_id` AS `email_account_id`,
        `email_account`.`name` AS `email_account_name`,
        `phone`.`full_number_normalized` AS `phone.full_number_normalized`
    FROM
        ((((((((`user_external`.`user_external_table` `user_external`
        LEFT JOIN `profile_user_external`.`profile_user_external_table` `profile_user_external` ON ((`profile_user_external`.`user_external_id` = `user_external`.`user_external_id`)))
        LEFT JOIN `profile`.`profile_table` `profile` ON ((`profile`.`profile_id` = `profile_user_external`.`profile_id`)))
        LEFT JOIN `user`.`user_table` `user` ON ((`user`.`user_id` = `profile`.`profile.main_user_id`)))
        LEFT JOIN `person`.`person_table` `person` ON ((`person`.`person_id` = `profile`.`main_person_id`)))
        LEFT JOIN `email_account_user_external`.`email_account_user_external_table` `email_account_user_external` ON ((`email_account_user_external`.`user_external_id` = `user_external`.`user_external_id`)))
        LEFT JOIN `email_account`.`email_account_table` `email_account` ON ((`email_account`.`email_account_id` = `email_account_user_external`.`email_account_id`)))
        LEFT JOIN `phone_profile`.`phone_profile_table` `phone_profile` ON ((`phone_profile`.`profile_id` = `profile`.`profile_id`)))
        LEFT JOIN `phone`.`phone_table` `phone` ON ((`phone`.`phone_id` = `phone_profile`.`phone_id`)))
    WHERE
        (((0 <> `user_external`.`is_test_data`)
            IS NOT TRUE)
            AND (`user_external`.`end_timestamp` IS NULL))
