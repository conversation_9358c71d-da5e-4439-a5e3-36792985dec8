-- user_external_duplicates.sql
SELECT COUNT(*)
FROM user_external.user_external_table user_external;

SELECT
-- *
 user_external_id, system_id, subsystem_id, username, is_refresh_token_valid, is_test_data, end_timestamp
 -- , COUNT(*)
FROM user_external.user_external_table user_external
-- WHERE
-- system_id IS NULL AND subsystem_id IS NULL and username = ""
-- username = "<EMAIL>"
-- GROUP BY system_id, subsystem_id, username, end_timestamp
-- HAVING COUNT(*)>1

SELECT *
FROM user_external.user_external_table user_external
WHERE username IN (SELECT username
FROM user_external.user_external_table user_external
GROUP BY system_id, subsystem_id, username, end_timestamp
HAVING COUNT(*)>1);




-- Disable foreign key checks
SET FOREIGN_KEY_CHECKS = 0;
DELETE
-- user_external_id, system_id, subsystem_id, username, end_timestamp, COUNT(*)
FROM user_external.user_external_table user_external
WHERE
-- system_id IS NULL AND subsystem_id IS NULL and username = ""
-- end_timestamp IS NOT NULL
-- is_test_data IS TRUE
user_external_id = 1799 OR user_external_id = 2332
;

SET FOREIGN_KEY_CHECKS = 1;