# https://python-poetry.org/docs/pyproject

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.poetry]
name = "entity-type-local"
# I believe we are still using the version from setup.py and not from here until potery will work
version = "0.0.1" # https://pypi.org/project/<project-name> i.e. https://pypi.org/project/storage-local/
description = "<project-name> Python Package"
readme = "README.md"
authors = [
    "Circlez.ai <<EMAIL>>",
]
