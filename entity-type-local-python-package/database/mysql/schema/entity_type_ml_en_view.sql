CREATE ALGORITHM = UNDEFINED DEFINER = `tal`@`%` SQL SECURITY DEFINER VIEW `entity_type`.`entity_type_ml_en_view` AS
SELECT `entity_type`.`entity_type_ml_table`.`id`                        AS `id`,
       `entity_type`.`entity_type_ml_table`.`lang_code`                 AS `lang_code`,
       `entity_type`.`entity_type_ml_table`.`title`          AS `title`,
       `entity_type`.`entity_type_ml_table`.`is_title_approved` AS `is_title_approved`,
       `entity_type`.`entity_type_ml_table`.`created_timestamp`         AS `created_timestamp`,
       `entity_type`.`entity_type_ml_table`.`created_user_id`           AS `created_user_id`,
       `entity_type`.`entity_type_ml_table`.`updated_timestamp`         AS `updated_timestamp`,
       `entity_type`.`entity_type_ml_table`.`updated_user_id`           AS `updated_user_id`,
       `entity_type`.`entity_type_ml_table`.`start_timestamp`           AS `start_timestamp`,
       `entity_type`.`entity_type_ml_table`.`end_timestamp`             AS `end_timestamp`
FROM (`entity_type`.`entity_type_ml_table`
    JOIN `entity_type`.`entity_type_table`)
WHERE ((`entity_type`.`entity_type_ml_table`.`entity_type_id` = `entity_type`.`entity_type_table`.`id`)
    AND (`entity_type`.`entity_type_ml_table`.`start_timestamp` <= CURTIME())
    AND ((`entity_type`.`entity_type_ml_table`.`end_timestamp` IS NULL)
        OR (`entity_type`.`entity_type_ml_table`.`end_timestamp` >= CURTIME()))
    AND (`entity_type`.`entity_type_ml_table`.`is_title_approved` = TRUE)
    AND (`entity_type`.`entity_type_ml_table`.`lang_code` = 'en'))

-- I'm not sure it is needed
/*
CREATE 
    ALGORITHM = UNDEFINED 
    DEFINER = `ron.b`@`%` 
    SQL SECURITY DEFINER
VIEW `entity_type`.`entity_type_view` AS
    SELECT 
        `entity_type`.`entity_type_table`.`id` AS `entity_type_id`,
        `entity_type`.`entity_type_table`.`created_timestamp` AS `created_timestamp`,
        `entity_type`.`entity_type_table`.`created_user_id` AS `created_user_id`,
        `entity_type`.`entity_type_table`.`updated_timestamp` AS `updated_timestamp`,
        `entity_type`.`entity_type_table`.`updated_user_id` AS `updated_user_id`,
        `entity_type`.`entity_type_table`.`start_timestamp` AS `start_timestamp`,
        `entity_type`.`entity_type_table`.`end_timestamp` AS `end_timestamp`
    FROM
        `entity_type`.`entity_type_table`
    WHERE
        ((`entity_type`.`entity_type_table`.`start_timestamp` <= CURTIME())
            AND ((`entity_type`.`entity_type_table`.`end_timestamp` IS NULL)
            OR (`entity_type`.`entity_type_table`.`end_timestamp` >= CURTIME())))

*/
