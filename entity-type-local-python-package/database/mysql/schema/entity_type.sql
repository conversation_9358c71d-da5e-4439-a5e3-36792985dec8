USE entity_type;

CREATE TABLE IF NOT EXISTS entity_type_table
(
    id                INT UNSIGNED UNIQUE AUTO_INCREMENT  NOT NULL COMMENT 'PK: entity_type_id',
    created_timestamp TIMESTAMP DEFAULT NOW()             NOT NULL,
    created_user_id   BIGINT UNSIGNED                     NOT NULL,
    updated_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_user_id   BIGINT UNSIGNED                     NOT NULL,
    start_timestamp   TIMESTAMP DEFAULT NOW()             NOT NULL,
    end_timestamp     TIMESTAMP DEFAULT NULL,
    <PERSON>IMAR<PERSON> KEY (id),
    FOREIGN KEY (created_user_id) REFERENCES `user`.`user_table` (id),
    FOREIGN KEY (updated_user_id) REFERENCES `user`.`user_table` (id)
);

CREATE TABLE IF NOT EXISTS entity_type_ml_table
(
    id                        INT UNSIGNED UNIQUE AUTO_INCREMENT  NOT NULL COMMENT 'PK: entity_type_ml_id',
    entity_type_id            INT UNSIGNED                        NOT NULL,
    lang_code                 CHAR(5)                             NOT NULL,
    title          VARCHAR(255)                        NOT NULL,
    is_title_approved BOOL      DEFAULT FALSE,
    created_timestamp         TIMESTAMP DEFAULT NOW()             NOT NULL,
    created_user_id           BIGINT UNSIGNED                     NOT NULL,
    updated_timestamp         TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_user_id           BIGINT UNSIGNED                     NOT NULL,
    start_timestamp           TIMESTAMP DEFAULT NOW()             NOT NULL,
    end_timestamp             TIMESTAMP DEFAULT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (entity_type_id) REFERENCES entity_type_table (id),
    FOREIGN KEY (created_user_id) REFERENCES `user`.`user_table` (id),
    FOREIGN KEY (updated_user_id) REFERENCES `user`.`user_table` (id)
);

CREATE UNIQUE INDEX idx_entity_type_id_lang_code ON entity_type_ml_table (entity_type_id, lang_code);

CREATE VIEW entity_type_view AS
SELECT `id` AS `entity_type_id`,
       `created_timestamp`,
       `created_user_id`,
       `updated_timestamp`,
       `updated_user_id`,
       `start_timestamp`,
       `end_timestamp`
FROM `entity_type_table`
WHERE (`start_timestamp` <= CURRENT_TIME()
    AND (`end_timestamp` IS NULL OR `end_timestamp` >= CURRENT_TIME())
          );

CREATE VIEW entity_type_ml_en_view AS
SELECT `entity_type_ml_table`.`id` AS `id`,
       `entity_type_ml_table`.`lang_code`,
       `entity_type_ml_table`.`title`,
       `entity_type_ml_table`.`is_title_approved`,
       `entity_type_ml_table`.`created_timestamp`,
       `entity_type_ml_table`.`created_user_id`,
       `entity_type_ml_table`.`updated_timestamp`,
       `entity_type_ml_table`.`updated_user_id`,
       `entity_type_ml_table`.`start_timestamp`,
       `entity_type_ml_table`.`end_timestamp`
FROM `entity_type_ml_table`
         JOIN `entity_type_table`
WHERE (`entity_type_ml_table`.`entity_type_id` = `entity_type_table`.`id`
    AND `entity_type_ml_table`.`start_timestamp` <= CURRENT_TIME()
    AND (`entity_type_ml_table`.`end_timestamp` IS NULL OR `entity_type_ml_table`.`end_timestamp` >= CURRENT_TIME())
    AND `entity_type_ml_table`.`is_title_approved` = TRUE
    AND `entity_type_ml_table`.`lang_code` = 'en'
          );
