============================= test session starts ==============================
platform linux -- Python 3.13.3, pytest-8.3.5, pluggy-1.5.0
rootdir: /repos/circlez/entity-type-local-python-package/entity-type-local-python-package
configfile: pytest.ini
plugins: cov-6.2.1, anyio-4.9.0, langsmith-0.4.5
LOGZIO_TOKEN is not set, logs will not be sent to Logz.io
LoggerLocal.py session=0YT0MM8B6TA5O7MB02RAAFQNVBSMI5
Please change developer_email to developer_email_address in the logger object of component_id=112 component_name=database_mysql_local\connector
get_return_variables: get_sql_username
get_return_variables: get_sql_username success.
get_return_variables: get_sql_hostname
get_return_variables: get_sql_hostname success.
get_return_variables: get_sql_password
get_return_variables: get_sql_password success.
get_return_variables: get_sql_port
get_return_variables: get_sql_port success.
Please change developer_email to developer_email_address in the logger object of component_id=177 component_name=circles_number_generator
Please change developer_email to developer_email_address in the logger object of component_id=206 component_name=database_mysql_local\generic_crud
Please change developer_email to developer_email_address in the logger object of component_id=116 component_name=entity-type-local-python-package
get_return_variables: database_info
get_return_variables: database_info success.
get_return_variables: set_schema
get_return_variables: set_schema success.
get_return_variables: _connect_to_db
get_return_variables: _connect_to_db success.
get_return_variables: __init__
get_return_variables: __init__ success.
get_return_variables: connect
get_return_variables: connect success.
Please change developer_email to developer_email_address in the logger object of component_id=116 component_name=entity-type-local-python-package
get_return_variables: __init__
get_return_variables: __init__ success.
collected 2 items

entity_type_local/tests/test_entity_type.py get_return_variables: __init__
get_return_variables: __init__ success.
get_return_variables: cursor
get_return_variables: cursor success.
get_return_variables: execute
get_return_variables: execute success.
get_return_variables: lastrowid
get_return_variables: lastrowid success.
get_return_variables: commit
get_return_variables: commit success.
get_return_variables: insert_entity_type_id_by_name
get_return_variables: insert_entity_type_id_by_name success.
get_return_variables: fetchone
get_return_variables: fetchone success.
get_return_variables: get_entity_type_id_by_name
get_return_variables: get_entity_type_id_by_name success.
..

- generated xml file: /repos/circlez/entity-type-local-python-package/entity-type-local-python-package/pytest.xml -
================================ tests coverage ================================
_______________ coverage: platform linux, python 3.13.3-final-0 ________________

Name                                          Stmts   Miss  Cover   Missing
---------------------------------------------------------------------------
entity_type_local/src/entity_type.py             16     11    31%   10-15, 19-20, 24-25, 29-30
entity_type_local/src/entity_types_local.py      52      8    85%   40-46, 64-65, 99-100
setup.py                                          4      4     0%   1-6
---------------------------------------------------------------------------
TOTAL                                            95     23    76%

3 files skipped due to complete coverage.
Coverage XML written to file coverage.xml
============================== 2 passed in 3.97s ===============================
