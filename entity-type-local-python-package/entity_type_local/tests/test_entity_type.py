from datetime import datetime

from logger_local.LoggerComponentEnum import <PERSON>gger<PERSON><PERSON>po<PERSON><PERSON><PERSON>
from logger_local.LoggerLocal import Logger
from src.entity_types_local import EntityTypesLocal

ENTITY_TYPE_COMPONENT_ID = 116
COMPONENT_NAME = 'entity-type-local-python-package'

logger_code_init = {
    'component_id': ENTITY_TYPE_COMPONENT_ID,
    'component_name': COMPONENT_NAME,
    'component_category':
        LoggerComponentEnum.ComponentCategory.Unit_Test.value,
    'testing_framework': LoggerComponentEnum.testingFramework.pytest.value,
    'developer_email': '<EMAIL>'
}
logger_local = Logger.create_logger(object=logger_code_init)

ENTITY_NAME = "Test " + str(datetime.now())
ENTITY_NAME_INVALID = "INVALID"
# TODO Please remove this and let the insert method to use GenericCrudMl
#  (otherwise UserContext.get_effective_user_id)
# TODO Do not use Magic Numbers, please write and use UsersLocal.get_test_user_id()
USER_ID = 5000091

entity_types_local = EntityTypesLocal()


def test_insert_select():
    logger_local.start(object={})
    # TODO Please make sure this method updates is_test_data = true when called from Unit Test
    # EntityTypesLocal.insert_entity_type_id_by_name(ENTITY_NAME, USER_ID)
    entity_types_local.insert_entity_type_id_by_name(ENTITY_NAME, USER_ID)
    # entity = EntityTypesLocal.get_entity_type_id_by_name(ENTITY_NAME)
    entity = entity_types_local.get_entity_type_id_by_name(ENTITY_NAME)
    assert entity is not None
    logger_local.end("Test succeeded", object={})


def test_select_invalid():
    logger_local.start(object={})
    # entity = EntityTypesLocal.get_entity_type_id_by_name(ENTITY_NAME_INVALID)
    entity = entity_types_local.get_entity_type_id_by_name(ENTITY_NAME_INVALID)
    assert entity is None
    logger_local.end("Test succeeded", object={})
