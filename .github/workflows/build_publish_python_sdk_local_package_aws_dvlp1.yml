name: Deploy Python SDK Local Package to PyPI.org AWS dvlp1
on:
  push:
    branches: [ dev ]
    #pull_request:
    # branches: [ main ]

env:
  brand_name: Circlez
  environment_name: dvlp1
  repo_name: python-sdk-remote-python-package
  package_directory: python_sdk_remote # Neede to generate dependencies.json

jobs:
  deploy:
    name: PythonSdkRemP()Dvlp1Old Build, Test, and Publish
    if: false
    runs-on: ubuntu-latest
    environment:
      # If using $environment_name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      name: dvlp1
      url: https://${{ env.environment_name }}.circ.zone
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v3
        with:
          python-version: '3.x'

      - name: Install dependencies
        run: |
          cd ./$repo_name
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Lint with flake8
        run: |
          python -m pip install flake8 pytest
          # stop the build if there are Python syntax errors or undefined names
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics     

      - name: Build package
        run: |
          cd ./$repo_name
          pip install build
          python -m build

      #run: |
      #    pytest
      # TODO: Please write Unit-Test in /<project-name>/tests directory
      # TODO: Please create <project-name>-graphql/restapi-python-package in a separate repo to test the published package
      - name: Test locally with pytest (Test the published package should be done by <project-name>-graphql/restapi Unit-Test)
        # pytest instead of python -m pytest
        run: |
          cd ./$repo_name
          pip install pytest-cov
          # TODO: Fix this line (i.e. --cov=...) so it will work with your modules
          # pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=app tests/ | tee pytest-coverage.txt
          pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=. | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0
        #run: python -m unittest discover -s tests -p 'test_*.py'
        env:
          BRAND_NAME: ${{ env.brand_name }}
          ENVIRONMENT_NAME: ${{ env.environment_name }}

          # TODO: Please comment and then delete all variables not being used.
          # Per Environment
          LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', env.environment_name)] }}

          RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', env.environment_name)] }}
          RDS_USERNAME: ${{ vars.RDS_USERNAME }}
          RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}

          PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', env.environment_name)] }}
          PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', env.environment_name)] }}

          AWS_ACCESS_KEY_ID: ${{ secrets[format('AWS_ACCESS_KEY_ID_{0}', env.environment_name)] }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', env.environment_name)] }}

          REGION: ${{ vars.AWS_DEFAULT_REGION }}
          BUCKET_NAME: ${{ vars.BUCKET_NAME }}

      # TODO: Please create pytest-coverage.txt and pytest.xml before this step
      - name: Pytest coverage comment
        uses: MishaKav/pytest-coverage-comment@main
        with:
          pytest-coverage-path: ./pytest-coverage.txt
          junitxml-path: ./pytest.xml

      - name: Check the output coverage
        run: |
          echo "Coverage Percantage - ${{ steps.coverageComment.outputs.coverage }}"
          echo "Coverage Color - ${{ steps.coverageComment.outputs.color }}"
          echo "Coverage Html - ${{ steps.coverageComment.outputs.coverageHtml }}"
          echo "Summary Report - ${{ steps.coverageComment.outputs.summaryReport }}"         
          echo "Coverage Warnings - ${{ steps.coverageComment.outputs.warnings }}"
          echo "Coverage Errors - ${{ steps.coverageComment.outputs.errors }}"
          echo "Coverage Failures - ${{ steps.coverageComment.outputs.failures }}"
          echo "Coverage Skipped - ${{ steps.coverageComment.outputs.skipped }}"
          echo "Coverage Tests - ${{ steps.coverageComment.outputs.tests }}"
          echo "Coverage Time - ${{ steps.coverageComment.outputs.time }}"
          echo "Not Success Test Info - ${{ steps.coverageComment.outputs.notSuccessTestInfo }}"

      # TODO: I've added a few more steps in storage-local-python-package to support poetry. Do we need them?

      # TODO: Please make sure you have created pyproject.toml file in the project directory and not root directory and uncomment the bellow lines and the version is patch every time we run the GHA
      - name: Patch the version number in pyproject.toml
        run: |
          pip install poetry
          cd ./$repo_name
          poetry version patch

      - name: Publish package
        uses: pypa/gh-action-pypi-publish@27b31702a0e7fc50959f5ad993c78deac1bdfc29
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
          packages_dir: ${{ env.repo_name }}/dist
          # verbose: true  # For debugging


  # Trying to place it before publish-python-sdk-remote-python-package-dvlp1 in dvlp1
  generate-dependency-json-and-graphs-dvlp1:
    name: generate-dependency-json-and-graphs-dvlp1
    runs-on: ubuntu-latest
    environment: dvlp1
    #needs: publish-python-sdk-remote-python-package-dvlp1
    steps:
      - uses: actions/checkout@v3
      # This step is independent and can be executed in a separate job
      - name: Generate dependency JSON and 7 dependencies tree graphs
        run: |
          echo "List of reports"
          # TODO repo_name -> repo_directory
          cd ./$repo_name/$package_directory
          echo "Running BeautifulSoup4"
          pip install BeautifulSoup4 requests importlib-metadata networkx matplotlib scipy
          echo "Running get_dependencies"
          python -m utils.get_dependencies

      # https://github.com/marketplace/actions/create-pull-request
      # TODO Shall we use PAT or GITHUB_TOKEN?
      # TODO update parameters in 'with'
      - name: Create Pull Request (for sql2code and other changes in the code)
        uses: peter-evans/create-pull-request@v6

  publish-python-sdk-remote-python-package-dvlp1:
    name: PythonSdkRemP()Dvlp1 Build, Test, and Publish
    #if: true
    if: github.ref=='refs/heads/dev'
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target-environments: [ dvlp1 ] # [ dvlp1, dvlp1, prod1 ]
    # TODO Is it mandatory?
    permissions:
      id-token: write      # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write      # build_publish_python_package
      pull-requests: write # build_publish_python_package
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      #component-name: event
      repo-name: python-sdk-remote-python-package
      #branch_name: ${{ github.ref }}
      #repo_directory_name: profile_reddit_restapi_imp_local_python_package
      repo-directory: python-sdk-remote-python-package
      #package_directory_name: database_mysql_local
      #sql2code_command_line: python .\.venv\Lib\site-packages\sql_to_code\sqltocode.py --schema database --table table_definition_table --output_path .\database_mysql_local\src\
      # At this point the GitHub Submodule brings only dev branch, so we can use it only after we merge Sql2Code to dev
      #sql2code_command_line: "python sql2code/sqltocode_local_python/sql_to_code/src/sqltocode.py --schema database --table table_definition_table --output_path ./database_mysql_local/src"
      #is_run_remote: true
      #is_rds_security_group: false
      #is_publish_to_test_pypi: false # Not working
      #is_publish_to_non_test_pypi: true


  run-user-context-remote-python-package-dvlp1-dev:
    name: UserConRemP(dev)Dvlp1 Run
    #if: false
    if: github.ref=='refs/heads/dev'
    needs: publish-python-sdk-remote-python-package-dvlp1
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ dvlp1 ] # [ dvlp1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: user-context-remote-python-package
      branch-name: dev
      # TODO Move the requirments.txt from the root directory
      repo-directory: user-context-remote-python-package
      package-directory: user_context_remote


  run-google-contact-local-python-package-dvlp1-dev:
    name: GooConLocP(dev)Dvlp1 Run
    # 1. As it is not ready, 2. As it needs manual authentication - Relevant? Can we delete those two lines?
    #if: false
    if: github.ref=='refs/heads/dev'
    needs: publish-python-sdk-remote-python-package-dvlp1
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ dvlp1 ] # [ dvlp1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: google-contact-local-python-package
      branch-name: dev
      # TODO Move the requirments.txt from the root directory
      repo-directory: . 
