name: Execute SQL Files (Create VIEWs, Report) in entity-type-local Python Package play1
on:
  push:
    branches: [ "BU-*" ] # We can't remove this as dev branch is block from deploying to play1
    paths:
      - reports/**
  pull_request:
    branches: [ dev ]

jobs:
  Execute-SQL:
    runs-on: ubuntu-latest
    environment: play1
    steps:
      - uses: actions/checkout@v3
      
      # This step is independent and can be executed in a separate job
      - name: Run the reports
        run: |
          echo "List of reports"
          ls ./reports/*.sql
          echo "Pipeline all *.sql to MySQL"
          cat ./reports/*.sql | mysql -u ${{ vars.READ_ONLY_RDS_USERNAME_PLAY1 }} -p"${{ secrets.READ_ONLY_RDS_PASSWORD_PLAY1 }}" -h ${{ vars.RDS_HOSTNAME_PLAY1 }}
