# This workflow will upload a Python Package using Twine when a release is created 
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python#publishing-to-package-registries

# This workflow uses actions that are not certified by GitHub.
# They are provided by a third party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: Build Publish Logger Local Python Package dvlp1

on:
  push:
    branches: [ dev ]
  pull_request:
    branches: [ main ]

env:
  brand_name: Circlez
  environment_name: dvlp1
  project_name: logger-local-python-package
jobs:
  publish-logger-local-python-package-dvlp1-old:
    name: LogLocP()Play1 Old Build, Test, and Publish
    if: false
    runs-on: ubuntu-latest
    environment:
      name: dvlp1 # If using $environment_name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      url: https://${{ env.environment_name }}.circ.zone
    steps:
      - uses: actions/checkout@v4.0.0

      - name: Setup Python
        uses: actions/setup-python@v3
        with:
          python-version: "3.x"

      - name: Install dependencies
        run: |
          cd ./$project_name
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Lint with flake8
        run: |
          python -m pip install flake8 pytest
          # stop the build if there are Python syntax errors or undefined names
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

      - name: Build package
        run: |
          cd ./$project_name
          pip install build
          python -m build

      - name: Run Tests
        run: |
          cd ./$project_name
          pip install pytest-cov
          pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=app tests/ | tee pytest-coverage.txt
        env:
          BRAND_NAME: ${{ env.brand_name }}
          ENVIRONMENT_NAME: ${{ env.environment_name }}
          LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', env.environment_name)] }}
          PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', env.environment_name)] }}
          PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', env.environment_name)] }}
          RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', env.environment_name)] }}
          RDS_USERNAME: ${{ vars.RDS_USERNAME }}
          RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}

      # TODO: Please create pytest-coverage.txt and pytest.xml before this step
      - name: Pytest coverage comment
        uses: MishaKav/pytest-coverage-comment@main
        with:
          pytest-coverage-path: ./pytest-coverage.txt
          junitxml-path: ./pytest.xml

      - name: Check the output coverage
        run: |
          echo "Coverage Percentage - ${{ steps.coverageComment.outputs.coverage }}"
          echo "Coverage Color - ${{ steps.coverageComment.outputs.color }}"
          echo "Coverage Html - ${{ steps.coverageComment.outputs.coverageHtml }}"
          echo "Summary Report - ${{ steps.coverageComment.outputs.summaryReport }}"
          echo "Coverage Warnings - ${{ steps.coverageComment.outputs.warnings }}"
          echo "Coverage Errors - ${{ steps.coverageComment.outputs.errors }}"
          echo "Coverage Failures - ${{ steps.coverageComment.outputs.failures }}"
          echo "Coverage Skipped - ${{ steps.coverageComment.outputs.skipped }}"
          echo "Coverage Tests - ${{ steps.coverageComment.outputs.tests }}"
          echo "Coverage Time - ${{ steps.coverageComment.outputs.time }}"
          echo "Not Success Test Info - ${{ steps.coverageComment.outputs.notSuccessTestInfo }}"

      # TODO: I've added a few more steps in storage-local-python-package to support poetry. Do we need them?

      # TODO: Please make sure you have created pyproject.toml file and uncomment the bellow lines and the version is patched every time we run the GHA
      - name: Patch the version number in pyproject.toml
        run: |
          pip install poetry
          cd ./$project_name
          poetry version patch

      - name: Publish package
        uses: pypa/gh-action-pypi-publish@27b31702a0e7fc50959f5ad993c78deac1bdfc29
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
          packages_dir: ${{ env.project_name }}/dist


  # play1 Build, Test, and Publish
  publish-logger-local-python-package-dvlp1:
    name: LogLocP()Play1 Build, Test, and Publish
    if: github.ref=='refs/heads/dev'
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: logger-local-python-package
      # Not needed for publish_ needed for run_
      #branch-name: dev
      repo-directory: logger-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging, should be commented
      #logger-minimum-severity: 1
      # TODO We added this to make logger-local pass the tests and publish
      logger-is-write-to-sql: true


  ############## dvlp1 sanity
  run-database-mysql-local-python-package-dvlp1-dev:
    name: DBMySQLLocalP(dev)Play1 Run
    #if: ${{ true }}
    if: github.ref=='refs/heads/dev'
    needs: publish-logger-local-python-package-dvlp1
    strategy:
      # So we'll try to execute jobs in all environments even if one of them failed
      #fail-fast: false
      matrix:
        python-version: [ '3.x' ] # Latest 3.x version (Currently 3.12)
        ## TODO We didn't manage to have multiple environments
        target_environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
      # TODO Is it mandatory?
      #permissions:
      #id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      #component-name: event
      repo-name: database-mysql-local-python-package
      branch-name: dev
      #repo-directory-name: profile_reddit_restapi_imp_local_python_package
      # TODO Misson _local_
      #repo-directory-name: profile_reddit_restapi_imp_python_package
      repo-directory: database-mysql-local-python-package
      #package-directory-name: database_mysql_local
      #sql2code-command-line: python .\.venv\Lib\site-packages\sql_to_code\sqltocode.py --schema database --table table_definition_table --output_path .\database_mysql_local\src\
      # At this point the GitHub Submodule brings only the dev branch, so we can use it only after we merge Sql2Code to dev
      #sql2code-command-line: "python sql2code/sqltocode_local_python/sql_to_code/src/sqltocode.py --schema database --table table_definition_table --output_path ./database_mysql_local/src"
      #is-run-local: true
      #is-rds-security-group: false
      #is-publish-to-test-pypi: false # Not working
      #is-publish-to-non-test-pypi is not defined in the referenced workflow.
      #is-publish-to-non-test-pypi: true


  run-database-mysql-local-python-package-dvlp1-1234:
    name: DBMySQLLocalP(1234)Play1 Run
    # Branch merged
    if: false
    #if: github.ref=='refs/heads/dev'
    needs: publish-logger-local-python-package-dvlp1
    strategy:
      # So we'll try to execute jobs in all environments even if one of them failed
      #fail-fast: false
      matrix:
        python-version: [ '3.x' ] # Latest 3.x version (Currently 3.12)
        ## TODO We didn't manage to have multiple environments
        target_environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
      # TODO Is it mandatory?
      #permissions:
      #id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      #component-name: event
      repo-name: database-mysql-local-python-package
      branch-name: BU-1234-Akiva-Skolnik
      #repo-directory-name: profile_reddit_restapi_imp_local_python_package
      # TODO Misson _local_
      #repo-directory-name: profile_reddit_restapi_imp_python_package
      repo-directory: database-mysql-local-python-package
      #package-directory-name: database_mysql_local
      #sql2code-command-line: python .\.venv\Lib\site-packages\sql_to_code\sqltocode.py --schema database --table table_definition_table --output_path .\database_mysql_local\src\
      # At this point the GitHub Submodule brings only the dev branch, so we can use it only after we merge Sql2Code to dev
      #sql2code-command-line: "python sql2code/sqltocode_local_python/sql_to_code/src/sqltocode.py --schema database --table table_definition_table --output_path ./database_mysql_local/src"
      #is-run-local: true
      #is-rds-security-group: false
      #is-publish-to-test-pypi: false # Not working
      #is-publish-to-non-test-pypi is not defined in the referenced workflow.
      #is-publish-to-non-test-pypi: true


  run-database-mysql-local-python-package-dvlp1-2292:
    name: DBMySQLLocalP(2292)Play1 Run
    if: false
    # if: github.ref=='refs/heads/dev'
    needs: publish-logger-local-python-package-dvlp1
    strategy:
      # So we'll try to execute jobs in all environments even if one of them failed
      #fail-fast: false
      matrix:
        python-version: [ '3.x' ] # Latest 3.x version (Currently 3.12)
        ## TODO We didn't manage to have multiple environments
        target_environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
      # TODO Is it mandatory?
      #permissions:
      #id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      #component-name: event
      repo-name: database-mysql-local-python-package
      branch-name: BU-2292--add-order-by-to-methods-in-GenericCRUDML--Tal-Goodman-
      #repo-directory-name: profile_reddit_restapi_imp_local_python_package
      # TODO Misson _local_
      #repo-directory-name: profile_reddit_restapi_imp_python_package
      repo-directory: database-mysql-local-python-package
      #package-directory-name: database_mysql_local
      #sql2code-command-line: python .\.venv\Lib\site-packages\sql_to_code\sqltocode.py --schema database --table table_definition_table --output_path .\database_mysql_local\src\
      # At this point the GitHub Submodule brings only the dev branch, so we can use it only after we merge Sql2Code to dev
      #sql2code-command-line: "python sql2code/sqltocode_local_python/sql_to_code/src/sqltocode.py --schema database --table table_definition_table --output_path ./database_mysql_local/src"
      #is-run-local: true
      #is-rds-security-group: false
      #is-publish-to-test-pypi: false # Not working
      #is-publish-to-non-test-pypi is not defined in the referenced workflow.
      #is-publish-to-non-test-pypi: true


  run-dialog-workflow-local-python-package-dvlp1-dev:
    name: DialogWFLocalPPlay1 Build Publish
    needs: publish-logger-local-python-package-dvlp1
    if: github.ref=='refs/heads/dev'
    #if: ${{ true }}
    strategy:
      matrix:
        target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
      # TODO Is it mandatory?
    #permissions:
    #id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: dialog-workflow-local-python-package
      branch-name: dev
      repo-directory: dialog-workflow-local-restapi-python-package

  # Running top-level Python executables
  publish-queue-worker-local-python-package-dvlp1:
    name: QueueWorkerLocalPPlay1 Build, Test & Publish
    #if: ${{ true }}
    if: github.ref=='refs/heads/dev'
    needs: publish-logger-local-python-package-dvlp1
    strategy:
      # So we'll try to execute jobs in all environments even if one of them failed
      #fail-fast: false
      matrix:
        python-version: [ '3.x' ] # Latest 3.x version (Currently 3.12)
        ## TODO We didn't manage to have multiple environments
        target_environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    # TODO Is it mandatory?
    permissions:
      id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write
      pull-requests: write
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      #component-name: event
      repo-name: queue-worker-local-python-package
      branch-name: dev
      #repo_directory_name: profile_reddit_restapi_imp_local_python_package
      # TODO Misson _local_
      #repo_directory_name: profile_reddit_restapi_imp_python_package
      repo-directory: queue_worker_local_python_package
      #package_directory_name: database_mysql_local
      #sql2code_command_line: python .\.venv\Lib\site-packages\sql_to_code\sqltocode.py --schema database --table table_definition_table --output_path .\database_mysql_local\src\
      # At this point the GitHub Submodule brings only dev branch, so we can use it only after we merge Sql2Code to dev
      #sql2code_command_line: "python sql2code/sqltocode_local_python/sql_to_code/src/sqltocode.py --schema database --table table_definition_table --output_path ./database_mysql_local/src"
      #is_run_local: true
      #is_rds_security_group: false
      #is_publish_to_test_pypi: false # Not working
      # is_publish_to_non_test_pypi is not defined in the referenced workflow.
      #is_publish_to_non_test_pypi: true

  publish-smartlink-local-python-package-dvlp1:
    name: SmartLinkLocalP()Play1 Build, Test & Publish
    if: github.ref=='refs/heads/dev'
    needs: publish-logger-local-python-package-dvlp1
    strategy:
      # So we'll try to execute jobs in all environments even if one of them failed
      #fail-fast: false
      matrix:
        python-version: [ '3.x' ] # Latest 3.x version (Currently 3.12)
        target_environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    permissions:
      id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write # TODO I added it to try allowing Sql2Code to update the repo. We might want to move the Sql2Code to a separate job (not step) and give permission to that job
      pull-requests: write # Allow Sql2Code to create Pull Request with the changes
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: smartlink-local-python-package
      repo-directory: smartlink_local_python_package
      branch-name: dev


  deploy-smartlink-restapi-python-serverless-com-dvlp1:
    name: SmartLinkRestPSC()Play1 Deploy
    if: github.ref=='refs/heads/dev'
    needs: publish-smartlink-local-python-package-dvlp1
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/deploy_python_serverless_com.yml@main
    secrets: inherit
    permissions:
      pull-requests: write # This alone did not solve the "Resource not accessible by integration." error
      contents: write # https://github.com/MishaKav/pytest-coverage-comment/issues/68#issuecomment-1143862564
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: smartlink-restapi-python-serverless-com
      branch-name: dev
      repo-directory: .
      #package-directory: .
      #is-run-remote: true


  # TODO We should make sure run- is using the latest logger version
  run-google-contact-local-python-package-dvlp1-dev:
    name: GoogleContactLocP()Play1 Build Publish
    if: github.ref=='refs/heads/dev'
    needs: publish-logger-local-python-package-dvlp1
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      #component-name: google-contact
      repo-name: google-contact-local-python-package
      branch-name: dev
      repo-directory: google-contact-local-python-package
      #TODO We should bring it back to dev ASAP
      #branch-name: dev
      #package_directory_name: google-contact_local
      #is_rds_security_group: false
      #is_publish_to_non_test_pypi: true
      # TODO Change <NAME_EMAIL> Google Account
      google-user: '<EMAIL>'


  publish-contact-person-profile-csv-imp-local-python-package-dvlp1:
    name: ImpLocalPPlay1 Build Publish Test
    if: github.ref=='refs/heads/dev'
    needs: publish-logger-local-python-package-dvlp1
    strategy:
      fail-fast: false
      matrix:
        target_environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      repo-name: contact-person-profile-csv-imp-local-python-package
      repo-directory: contact-person-profile-csv-imp-local
      branch-name: dev


  publish-profile-facebook-selenium-scraper-imp-local-python-package-dvlp1:
    name: ProfileFBSeleScraImpP()Play1 Build, Test, and Publish
    if: github.ref=='refs/heads/dev'
    needs: publish-logger-local-python-package-dvlp1
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: profile-facebook-selenium-scraper-imp-local-python-package
      # Not needed for publish_ needed for run_
      branch-name: dev
      repo-directory: profile-facebook-selenium-scraper-imp-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging, should be commented
      #logger-minimum-severity: 1


  run-text-block-local-python-package-dvlp1-dev:
    name: TxtBlkLocP(dev)Dvlp1 Build, Test & Publish
    if: github.ref=='refs/heads/dev'
    needs: publish-logger-local-python-package-dvlp1
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: text-block-local-python-package
      # Not needed for publish_ needed for run_
      branch-name: dev
      repo-directory: text-block-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging, should be commented
      #logger-minimum-severity: 1


  # Running the equivalent TypeScript 
  # As the logger is a very critical component, although there is no real dependency (except the logger schema), we decided to run also Logger TypeScript
  publish-logger-local-typescript-package-dvlp1:
    name: LoggerLocTS()Play1 Build, Test, and Publish
    # TODO Not working as we need to patch the version before publishing
    if: false
    #No need to add "needs" here
    #needs:
    #TODO It seems the if on the dependabot branch is not working
    #if: github.ref=='refs/heads/dev'
    #if: ${{ startsWith(github.ref, 'refs/heads/bu-') || startsWith(github.ref, 'refs/heads/dependabot') }}
    #if: startsWith(github.ref, 'refs/heads/dependabot')
    needs: publish-logger-local-python-package-dvlp1
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/publish_typescript_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: logger-local-typescript-package
      repo-directory: logger-local
      branch-name: dev
      #package-directory: .
      #is_bump_version: false
      #is_rds_security_group: false


  # TODO This is not working probably due to a permission issue
  # We should deploy and not run, so the remote will use this code.
  deploy-logger-restapi-typescript-serverless-com-dvlp1-dev:
    name: LoggerRestTSSCPlay1 Build, Test, and Publish
    needs: publish-logger-local-typescript-package-dvlp1
    if: github.ref=='refs/heads/dev'
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/deploy_typescript_serverless_com.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand_name: Circlez
      environment-name: ${{ matrix.target_environments }}
      #token: ${{ secrets.GH_FINE_GRAINED_READ_REPO_CONTENTS_TOKEN }}
      repo-name: logger-restapi-typescript-serverless-com
      # `npm update` failed with a "Permission denied" error message, maybe because we didn't provide the exact branch-name
      #branch-name: BU-1471-When-environments-variables-in-Lambda-are-wrong-Logger-REST-API-returns-OK-while-is-suppose-to-return-error
      branch-name: dev
      #repo-directory: server-side
      repo-directory: logger-restapi-typescript-serverless-com
      #package-direcotry: RemoteLoggerServerSide-serverless
      #TODO Is it mandatory?
      package-directory: remote-logger-server-Side-serverless


  run-logger-remote-typescript-package-build-publish-dvlp1-dev:
    name: LogRemoteTS(dev)Play1 Run
    needs: deploy-logger-restapi-typescript-serverless-com-dvlp1-dev
    if: github.ref=='refs/heads/dev'
    strategy:
      # To allow running multiple environments. Unfortunately, we need to change the other tasks to "if: always()"
      fail-fast: false
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_typescript_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      repo-name: logger-remote-typescript-package
      # We should not state the branch as we want the current branch
      branch-name: dev
      repo-directory: .
      #is_bump_version: false
      #is_rds_security_group: false
