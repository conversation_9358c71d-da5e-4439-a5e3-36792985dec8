name: Build Publish Entity Type Local Python Package to PyPI.org play1
on:
  push:
    branches: [ "BU-*" ]
  # pull_request:
  #   branches: [dev]

env:
  env_name: play1
  # TODO: make sure all our code is in a project directory and not in the root. Under the project directory we have src directory i.e. /entity-type-local/src We should update the directory name here. In the root directory we should have only .gitignore
  project_name: entity-type-local-python-package

#permissions:
#contents: read

jobs:
  deploy:
    if: false
    runs-on: ubuntu-latest
    environment:
      name: play1 # If using $env_name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      url: https://${{ env.env_name }}.circ.zone

    #strategy:
    #fail-fast: false
    #matrix:
    #python-version: [ "3.11" ]

    steps:
      - uses: actions/checkout@v3.5.3 # v3 -> v3.5.3

      - name: Setup Python
        uses: actions/setup-python@v4.7.0 # v3 -> v4.7.0
        with:
          python-version: "3.x"
          #python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          cd ${{ env.project_name }}
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          #if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

      - name: Lint with flake8
        run: |
          python -m pip install flake8 pytest
          # stop the build if there are Python syntax errors or undefined names
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

      - name: Build package
        run: |
          cd ${{ env.project_name }}
          pip install build
          python -m build

      #run: |
      #    pytest
      # TODO: Please write Unit-Test
      # TODO: Please create entity-type-local-remote-typescript-package in a seperate repo to test the published package from TypeScript
      # - name: Test local with pytest (test of published package should be by entity-type-local-remote package/rep)
      #   # I added --junitxml=pytest.xml for MishaKav/pytest-coverage-comment
      #   run: |
      #         pytest --junitxml=pytest.xml # Instead of python -m pytest
      #   #run: python -m unittest discover -s tests -p 'test_*.py'
      #   env:
      #     # Per Environment
      #     LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', env.env_name)] }}
      #     RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', env.env_name)] }}
      #     RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}
      #     # Per Microservice
      #     RDS_USERNAME: ${{ vars.RDS_USERNAME }}
      #     AWS_ACCESS_KEY_ID: ${{ secrets[format('AWS_ACCESS_KEY_ID_{0}', env.env_name)] }}
      #     AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', env.env_name)] }}
      #     REGION: ${{vars.AWS_DEFAULT_REGION}}
      #     BUCKET_NAME: ${{vars.BUCKET_NAME}}
      #     ENVIRONMENT_NAME: ${{ env.env_name }}

      #- name: Create JUnitXML
      #run: py.test sample_tests.py --junitxml=C:\path\to\out_report.xml
      #- name: Create pytest-coverage.txt for MishaKav/pytest-coverage-comment
      #run: pytest --cov=${{ env.project_name }}/src --cov-report=term-missing > pytest-coverage.txt

      # TODO: Require pytest-coverage.txt and pytest.xml
      #- name: Pytest coverage comment
      #uses: MishaKav/pytest-coverage-comment@main
      #with:
      #pytest-coverage-path: ./pytest-coverage.txt
      #junitxml-path: ./pytest.xml

      - name: Publish package
        uses: pypa/gh-action-pypi-publish@27b31702a0e7fc50959f5ad993c78deac1bdfc29
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
          packages_dir: ${{ env.repo_name }}/dist
          # verbose: true  # For debugging


# play1 Build, Test, and Publish
  publish-entity-type-local-python-package-play1:
    name: EntityTypLocP()Play1 Build, Test, and Publish
    if: startsWith(github.ref, 'refs/heads/bu-')
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: entity-type-local-python-package
      # Not needed for publish_ needed for run_
      #branch-name: dev
      repo-directory: entity-type-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging, should be commented
      #logger-minimum-severity: 1


  run-smartlink-local-python-package-build-publish-play1-dev:
    name: SmartLinkLocalP(dev)Play1 Run
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-entity-type-local-python-package-play1
    # TODO Is it mandatory?
    permissions:
      id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      #environment-name: ${{ matrix.target-environments }}
      repo-name: smartlink-local-python-package
      branch-name: dev
      repo-directory: smartlink-local-python-package
      is-run-local: true


  run-smartlink-local-python-package-build-publish-play1-2151:
    name: SmartLinkLocalP(2151)Play1 Run
    #if: startsWith(github.ref, 'refs/heads/bu-')
    if: false
    needs: publish-entity-type-local-python-package-play1
    # TODO Is it mandatory?
    permissions:
      id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      #environment-name: ${{ matrix.target-environments }}
      repo-name: smartlink-local-python-package
      branch-name: BU-2151-Develop-feature-to-create-the-SmartLink-Akiva-Skolnik
      repo-directory: smartlink_local_python_package
      is-run-local: true
