name: Build, Test & Deploy Python Serverless.com Lambda AWS  

# https://github.com/circles-zone/smartlink-restapi-python-serverless-com/blob/BU-2151-Develop-feature-to-create-the-SmartLink-Akiva-Skolnik/.github/workflows/build_deploy_smartlink_restapi_python_serverless_com_play1.yml

on:
  workflow_call:
    inputs:
      brand-name:
        required: false
        type: string
        default: Circlez
      environment-name:
        required: false
        type: string
        default: play1
      repo-name:
        required: false
        type: string
      branch-name:
        required: false
        type: string
        #default: dev
        default: ${{ github.ref }}
      repo-directory:
        required: true
        type: string
      #package_directory_name:
        #required: true
        #type: string
      is-rds-security-group:
        required: false
        type: boolean
        default: false
      component-name:
        required: false
        type: string
      #is_run_local:
        #required: false
        #type: string    
      #is_run_remote:
        #required: false
        #type: string
      logger-minimum-severity:
        required: false
        type: string
        default: Warning
      # TODO is-logger-write-to-sql
      logger-is-write-to-sql:
        required: false
        type: boolean
        default: false
        
jobs:
  python_serverless_com_build_deploy:
    # Begin with name exaxtly like the yml filename
    name: Deploy Python Serverless.com ${{ inputs.component-name }} (Both Restapi and our own GraphQL)
    if: ${{contains(github.event.head_commit.message, '[pub]') }}
    runs-on: ubuntu-latest
    # https://github.com/circles-zone/dialog-workflow-restapi-python-serverless-com/actions Needs more than 5 minutes
    timeout-minutes: 10
    # https://github.com/MishaKav/pytest-coverage-comment/issues/68 "Resource not accessible by integration." error
    permissions:
      pull-requests: write # Alone didn't resolve
      contents: write # Needed for step "pytest-coverage-comment", variable-local-python-package,  https://github.com/MishaKav/pytest-coverage-comment/issues/68#issuecomment-1143862564
    environment:
      name: ${{ inputs.environment-name }} # If using $environment-name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      url: https://${{ inputs.environment-name }}.circ.zone
    env:
      # if 20.x and not '20.x' we get warning : "Unexpected input(s) 'node-version', valid inputs are ['']"
      NODE_VERSION: 20 # 14.x -> 20.x used for installing the serverless-python-requirements dependency from NPM
    strategy:
      matrix:
        # TODO should be the same version as in the serverless.yml provider: runtime:
        python-version:
          #- 3.8 # With a build matrix, you can specify a list of Python versions to deploy with
          - 3.11 # Works as serverless.yml supports Python3.11 and do not supports Ptyhon3.12 as of today. 
          #- 3.12 # # We changed from python-version 3.x (which was 3.12) to 3.10 so we can use torch in requirments.txt in Python Package
          #- 3.x # currently CPython 3.12.0. We can't work with 3.12, 3.x as serverless.yml works with python3.11
    steps:
    # --------- use Python to install Python dependencies and run linter, tests, etc. ---------
    - uses: actions/checkout@main # v4.1.1 # https://github.com/actions/checkout
      with:
        token: ${{ secrets.GH_FINE_GRAINED_READ_REPO_CONTENTS_TOKEN }}
        repository: circles-zone/${{ inputs.repo-name }}
        ref: ${{ inputs.branch-name }}

    - name: Setup Python ${{ matrix.python-version }}
      uses: actions/setup-python@main # v5.1.0 # https://github.com/actions/setup-python
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip
      uses: actions/cache@main # v3.3.2 # https://github.com/actions/cache
      with:
        # This path is specific to the Ubuntu
        path: ~/.cache/pip
        # Look to see if there is a cache hit for the corresponding requirements file
        key: ${{ runner.os }}-pip-${{ hashFiles('requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          ${{ runner.os }}-
          
    - name: Install dependencies
      run: |
        cd ./${{ inputs.repo-directory }}
        python -m pip install --upgrade pip
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

    # Added recently      
    # Do we need it in serverless.com
    # Do not need setup.py for serverless.com repo (unless we want also to create package)
    - name: Build package (use setup.py if exists)
      run: |
        cd ./${{ inputs.repo-directory }}
        pip install build
        python -m build
    
    # Added recently      
    - name: Lint with flake8
      run: |
        python -m pip install flake8 pytest
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics        

    # TODO Node.js 16 actions are deprecated. Please update the following actions to use Node.js 20: haythem/public-ip@master
    # If this repo accesses the database
    - name: Get GitHub Action (GHA) runner IP
      if: inputs.is-rds-security-group == true
      id: ip
      uses: haythem/public-ip@master # v1.3 https://github.com/haythem/public-ip
      with:
        maxRetries: 50

    - name: Setting AWS_DEFAULT_REGION and AWS_SG_NAME environment variables.
      if: inputs.is-rds-security-group == true
      run: |
        echo "AWS_DEFAULT_REGION=us-east-1" >> $GITHUB_ENV
        # RDS/MySQL EC2 Security Group in Management/Master AWS Account 
        echo "AWS_SG_NAME=mysql_mang_sg" >> $GITHUB_ENV

    - name: Add GitHub Actions (GHA) runner IP to EC2 Security Group in Master/Management AWS Account
      if: inputs.is-rds-security-group == true
      run: |
        aws ec2 authorize-security-group-ingress --group-name ${{ env.AWS_SG_NAME }} --protocol tcp --port 3306 --cidr ${{ steps.ip.outputs.ipv4 }}/32
      env:
        # Since RDS/MySQL is currently in Management/Master AWS Account
        AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', 'MANG1')] }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', 'MANG1')] }}
        AWS_DEFAULT_REGION: ${{ env.AWS_DEFAULT_REGION }}


    - name: Set environment variables
      run: |
        if ${{ inputs.repo-name == 'whatsapp-message-inforu-local-python-package-serverless-com' }} || ${{ inputs.repo-name == 'xxx' }} ; then
          echo "Setup Messages Environment Variables"
          echo "MESSAGE_TEST_TO_PHONE_NUMBER=${{ vars[format('MESSAGE_TEST_TO_PHONE_NUMBER_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not a Messages repo"
        fi

        if ${{ inputs.repo-name == 'opensearch-local-python-package' ||
          inputs.repo-name == 'event-main-local-restapi-python-package-serverless-com' }}; then
          echo "Setup OPENSEARCH Environment Variables"
          echo "OPENSEARCH_HOST=${{ vars[format('OPENSEARCH_HOST_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          echo "OPENSEARCH_INITIAL_ADMIN_PASSWORD=${{ secrets[format('OPENSEARCH_INITIAL_ADMIN_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        else
          echo "Not OPENSEARCH repo"
        fi


    - name: Test local package with pytest (test of Serverless.com REST/API deployment should be by ${{ inputs.component_name}}-remote)
      run: |
        cd ./${{ inputs.repo-directory }}
        pip install pytest-cov
        pip install pytest-mock
        #pytest # Instead of python -m pytest
        # TODO Also in https://github.com/circles-zone/github-workflows/blob/main/.github/workflows/publish_python_package.yml
        set -o pipefail
        pytest -s --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov-report=xml:coverage.xml --cov=. | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0
      env:
        # TODO: Please comment and then delete all variables not being used.
        BRAND_NAME: ${{ inputs.brand-name }}
        ENVIRONMENT_NAME: ${{ inputs.environment-name }}
        
        LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', inputs.environment-name)] }}
        # https://github.com/circles-zone/logger-local-python-package/blob/dev/logger-local-python-package/logger_local/src/MessageSeverity.py
        LOGGER_MINIMUM_SEVERITY: ${{ inputs.logger-minimum-severity }}
        LOGGER_IS_WRITE_TO_SQL: ${{ inputs.logger-is-write-to-sql }}
        
        PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}
        PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}
        
        # Database (RDS) credential must be here as we need them for all deployable serverless.com, test with SANITY_RDS_USERNAME and deployment with RDS_USERNAME

        RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}
        #RDS_USERNAME: ${{ vars.RDS_USERNAME }}
        RDS_USERNAME: ${{ vars.SANITY_RDS_USERNAME }}        
        #RDS_PASSWORD: ${{ secrets.SANITY_RDS_PASSWORD }}
        RDS_PASSWORD: ${{ secrets[format('SANITY_RDS_PASSWORD_{0}', inputs.environment-name)] }}

    - name: Remove Github Actions IP from a security group
      #if: always()
      if: inputs.is-rds-security-group == true
      run: |
        aws ec2 revoke-security-group-ingress --group-name ${{ env.AWS_SG_NAME }} --protocol tcp --port 3306 --cidr ${{ steps.ip.outputs.ipv4 }}/32
      env:
        # Since RDS/MySQL in Master/Management AWS Account
        AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', 'MANG1')] }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', 'MANG1')] }}
        AWS_DEFAULT_REGION: ${{ vars.AWS_DEFAULT_REGION }}

    # Added recently      
    # TODO: Please create pytest-coverage.txt and pytest.xml before this step
    - name: Pytest coverage comment
      #uses: MishaKav/pytest-coverage-comment@main
      uses: circles-zone/pytest-coverage-comment@main
      with:
        # https://github.com/MishaKav/pytest-coverage-comment/issues/68 "Resource not accessible by integration." error
        #token: {{ secrets.GITHUB_TOKEN }}
        pytest-coverage-path: ./pytest-coverage.txt
        junitxml-path: ./pytest.xml
        #create-new-comment: true
        
    # TODO: I've added a few more steps in storage-local-python-package to support poetry. Do we need them?
     
    # --------- Use Node and NPM to install serverless-python-requirements ---------
    - name: Use Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@main # v4.0.0 # https://github.com/actions/setup-node
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Cache node modules
      uses: actions/cache@main # v3.3.2 # https://github.com/actions/cache
      env:
        cache-name: cache-node-modules
      with:
        # npm cache files are stored in `~/.npm` on Linux/macOS
        path: ~/.npm
        key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
        restore-keys: |
          ${{ runner.os }}-build-${{ env.cache-name }}-
          ${{ runner.os }}-build-
          ${{ runner.os }}-

    # TODO: Please make sure you have created pyproject.toml file in the project directory and not in the root directory and uncomment the bellow lines and the version is patch every time we run the GHA
    - name: Patch the version number in pyproject.toml
      run: |
        pip install poetry
        cd ./${{ inputs.repo-directory }}
        poetry version patch
        
    # Needed only? for the Serverless Deploy
    # If you get the error "npm WARN tar TAR_ENTRY_INVALID checksum failure" remove comments from package.json
    - name: Install Node dependencies (specifically, serverless-python-requirements)
      run: |
        cd ./${{ inputs.repo-directory }}
        #npm install
        npm update

    # https://github.com/serverless/serverless/issues/12503
    #- name: Install Serverless
      #run: sudo npm install -g serverless
    - name: Serverless downgrade to 3.38.0 (actually install 3.39.0) - I think it is not needed anymore
      # if: always()
      if: false
      run: |
        # Error: This command can only be run in a Serverless service directory. Make sure to reference a valid config file in the current working directory if you're using a custom config file
        echo "pwd"
        pwd
        echo "cd ${{ inputs.repo-directory }} (to repo-directory)"
        cd ${{ inputs.repo-directory }}

        # In the past we didn't have to run `npm install -g serverless` before dhollerbach/actions.serverless-with-python-requirements
        # To resolve "You must sign in or use a license key with Serverless Framework V.4 and later versions. Please use "serverless login"."
        echo "npm install -g serverless@3.38.0"
        npm install -g serverless@3.38.0
        echo "serverless --version"
        serverless --version

        # TODO Shall we change the directory before "Serverless Deployment"?
        echo "1 pwd"
        pwd
        ls -lag

        #echo "2 cd ./${{ inputs.repo-directory }}"
        #cd ./${{ inputs.repo-directory }}
        #pwd
        #ls -lag

        # As it appers in serveless.yml we must install it, do we need to do it?
        # Should run in Sererless service direcroty (repo-directory)
        
        echo "NOT serverless plugin install -n serverless-offline (In Serverless service directory) - TODO This fails"
        #serverless plugin install -n serverless-offline
        #echo "After serverless plugin install -n serverless-offline"

        # We got error "ERESOLVE could not resolve" from serverless-python-requirements. TODO Maybe we need npm u?
        #echo "serverless plugin install -n serverless-python-requirements"
        #serverless plugin install -n serverless-python-requirements

    - name: Set environment variables for Serverless Deploy
      run: |
        # Since it is Serverless, if is-run-local is redundent
        #if [[ ${{ inputs.is-run-local }} ]]; then
          echo "is-run-local==true so setup RDS environment"
          echo "RDS_HOSTNAME=${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # TODO RDS_USERNAME and RDS_PASSWORD are not good in case logger-local-python-package running smartline-rest-python-serverless-com as we get logger_user instead of smartlink_user
          echo "RDS_USERNAME=${{ vars.SANITY_RDS_USERNAME }}" >> $GITHUB_ENV
          echo "RDS_PASSWORD=${{ secrets[format('SANITY_RDS_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
          # In local and remote which need access to the database to get_test_entity_id()
          # Sanity tests might need other permission from the package runtime in production / serverless deployment
        #else # inputs.is-run-remote
          #echo "Not RDS repo"
        #fi

        # TODO If works in Serverless Doctor wen can remove those from Serverless.com Deploy V3/4 step
        echo "PRODUCT_USER_IDENTIFIER=${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV
        echo "PRODUCT_PASSWORD=${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}" >> $GITHUB_ENV

    # Unfortunately the serverless.yml must be in the root directory
    - name: Serverless.com Deploy V3/4 (must install the serverless plugin locally and push)
      # uses: dhollerbach/actions.serverless-with-python-requirements@master # No @main v2 https://github.com/dhollerbach/actions.serverless-with-python-requirements
      # Serverless.com v3
      # uses: talr/actions.serverless-with-python-requirements@master # No @main v2 https://github.com/dhollerbach/actions.serverless-with-python-requirements
      uses: dhollerbach/actions.serverless-with-python-requirements@master
      # Serverless.com v4 (Same one used in deploy_typescript_serverless_com.yml)
      # To resolve "Running sls config credentials is not supported in Serverless V4" in talr/actions.serverless-with-python-requirements@master
      # TODO The bellow didn't deplyed in AWS
      # uses: serverless/github-action@master # v4 # https://github.com/serverless/github-action
      # with:
        # TODO Node.js 16 actions are deprecated. Please update the following actions to use Node.js 20: haythem/public-ip@master
        # We get this warning: "Unexpected input(s) 'node-version', valid inputs are ['']"
        # node-version: ${{ env.NODE_VERSION }}
        # node-version: '20'
        # TODO Try to send it, but not in-use currently. This is not working yet, so we currently using REPO_DIRECTORY
        # repo-directory: ${{ inputs.repo-directory }}
      env:
        # Please do not add PRODUCT_USER_IDENTIFIER and PRODUCT_PASSWORD here
        # Needed only for (TODO)
        #      https://github.com/circles-zone/smartlink-local-restapi-python-serverless-com - As an anonymous user is pressing the SmartLink
        # Since this is Serverless.com deployment we prefer not to use our fixed PRODUCT_USER_IDENTIFIER AND PRODUCT_PASSWORD 
        PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', inputs.environment-name)] }}
        PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', inputs.environment-name)] }}

        # Due to "Region undefined" error and "Warning: Invalid configuration encountered at 'provider.region': must be equal to one of the allowed values" error
        REGION: us-east-1
        
        # TODO: Please comment and then delete all variables not being used.

        # This is not working, it keeps using Stage "dev"
        #STAGE: ${{ inputs.environment-name }}
        
        BRAND_NAME: ${{ inputs.brand-name }}
        ENVIRONMENT_NAME: ${{ inputs.environment-name }} # Needed also for CORS

        # Used in serverless.yml, TODO is it mandatory?
        STAGE: ${{ inputs.environment-name }}

        # TODO Comment and then delete JWT_SECRET_KEY
        JWT_SECRET_KEY: ${{ secrets[format('USER_JWT_SECRET_KEY_{0}', inputs.environment-name)] }}
        USER_JWT_SECRET_KEY: ${{ secrets[format('USER_JWT_SECRET_KEY_{0}', inputs.environment-name)] }}

        # TODO Our logger should work also when LOGZIO_TOKEN is not defined
        LOGZIO_TOKEN: ${{secrets[format('LOGZIO_TOKEN_{0}', inputs.environment-name)]}}
        # https://github.com/circles-zone/logger-local-python-package/blob/dev/logger-local-python-package/logger_local/src/MessageSeverity.py
        LOGGER_MINIMUM_SEVERITY: ${{ inputs.logger-minimum-severity }}        

        # Trying to resolve the error: "Unable to login in non-interactive mode. Please use a license key or access key, both of which you can get from the Serverless Framework Dashboard"
        # Needed for serverless.com V4
        SERVERLESS_ACCESS_KEY: ${{ secrets[format('SERVERLESS_ACCESS_KEY_{0}', inputs.environment-name)] }}
        # or if using AWS credentials directly
        AWS_ACCESS_KEY_ID: ${{ vars[format('AWS_ACCESS_KEY_ID_{0}', inputs.environment-name)] }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', inputs.environment-name)] }}

        # Database (RDS) credential must be here as we need them for all deployable serverless.com, test with SANITY_RDS_USERNAME and deployment with RDS_USERNAME
        RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}
        # RDS_USERNAME RDS_PASSWORD is not working good in logger-local-python-package executing smartlink-rest-python-serverless-com (as it takes the RDS_USERNAME of root/original repo)
        #RDS_USERNAME: ${{ vars.RDS_USERNAME }}
        #RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}

        # TODO Checking
        REPO_DIRECTORY: ${{ inputs.repo-directory }}

        # Serverless.com V4 CI/CD i.e. GHA I don't see change (TODO Shall we delete it)
        SLS_INTERACTIVE_DISABLE: true
        CI: true

    # TODO Since we don't have active subscription shall we comment this step in Serverless.com V4?
    - name: Serverless.com V4 Support (Instead of Serverless.com V3 Doctor) - This step is not mandatory (REST API deployed even if this step failed)
      if: always()
      run: |
        # It was enough to run it here before, but now we installed it before because of "You must sign in or use a license key with Serverless Framework V.4 and later versions. Please use "serverless login"."  
        #npm install -g serverless
        # Checking if doing "serverless login" before the "serverless doctor" will remove the error message
        # Comment serverless login as it show link in the GHA and block running.
        #echo "serverless login"
        #serverless login
        # Old version of serverless doctor which we are using do not supports --debug
        # Serverless.com V3
        # echo "serverless doctor"
        # serverless doctor
        # Serverless.com V4 (login and support) - support works only to customers with active subscriptio
        serverless login
        echo "sererless support"
        serverless support
      # TODO Delete as I'm not sure we need it
      env:
        # Needed for serverless.com V4
        SERVERLESS_ACCESS_KEY: ${{ secrets[format('SERVERLESS_ACCESS_KEY_{0}', inputs.environment-name)] }}

        BRAND_NAME: ${{ inputs.brand-name }}
        ENVIRONMENT_NAME: ${{ inputs.environment-name }} # Needed also for CORS

        # TODO Comment and then delete JWT_SECRET_KEY
        JWT_SECRET_KEY: ${{ secrets[format('USER_JWT_SECRET_KEY_{0}', inputs.environment-name)] }}
        USER_JWT_SECRET_KEY: ${{ secrets[format('USER_JWT_SECRET_KEY_{0}', inputs.environment-name)] }}

        # TODO Our logger should work also when LOGZIO_TOKEN is not defined
        LOGZIO_TOKEN: ${{secrets[format('LOGZIO_TOKEN_{0}', inputs.environment-name)]}}
        # https://github.com/circles-zone/logger-local-python-package/blob/dev/logger-local-python-package/logger_local/src/MessageSeverity.py
        LOGGER_MINIMUM_SEVERITY: ${{ inputs.logger-minimum-severity }}        

        # Database (RDS) credential must be here as we need them for all deployable serverless.com, test with SANITY_RDS_USERNAME and deployment with RDS_USERNAME
        RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', inputs.environment-name)] }}


    - name: TODO Update endpoint URL in url-remote-python and url-remote-typescript manually
      run: |
        echo "TODO Write powershell script that make sure that we update the endpoint URL in both url-remote-python and url-remote-typescript. Make sure we run this script from both deploy_typescript_serverless_com and deploy_python_serverless_com GHA yml"


    - name: TODO Update endpoint URL in GCP Project Auth 2.0 manually (and GHA Reusable workflows)
      run: |
        echo "TODO Write script that in the case of google-account-restapi-python-serverless-com repo it also update the endpoint URL in the relevant GCP Project Auth 2.0 Authorized URI (and if needed also in the Reusable GHA workflows)"
