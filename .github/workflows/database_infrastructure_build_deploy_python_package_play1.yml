name: Build Publish Database Infrastructure Local Python Package to PyPI.org play1
on:
  push:
    branches: [ "BU-*" ]
  # pull_request:
    # branches: [ dev ]

env:
  env_name: play1
  project_name: database-infrastructure-local-python-package
  package_directory: circles_number_generator

  #permissions:
  #contents: read

jobs:
  publish-database-infrastructure-local-python-package-play1-old:
    name: DBInfLocP()Play1 OLD Build, Test & Publish
    if: false
    runs-on: ubuntu-latest
    environment:
      name: play1 # If using $env_name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      url: https://${{ env.env_name }}.circ.zone

        #strategy:
        #fail-fast: false
        #matrix:
      #python-version: ["3.11"]
      #poetry-version: ["2.1.6"]
    steps:
      - uses: actions/checkout@v3.5.3 # v3 -> v3.5.3

      - name: Setup Python
        uses: actions/setup-python@v4.7.0 # v3 -> v4.7.0
        with:
          python-version: '3.x'
          #python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          cd ./$project_name
          pip install -r requirements.txt
          #if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

      - name: Lint with flake8
        run: |
          python -m pip install flake8 pytest
          # stop the build if there are Python syntax errors or undefined names
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics        

      - name: Build package
        run: |
          cd ./$project_name
          pip install build
          python -m build

      #run: |
      #    pytest
      # TODO: Please write Unit-Test
      # TODO: Please create <project-name>-remote-typescript-package in a seperate repo to test the published package from TypeScript
      - name: Test local with pytest (test of published package should be by <project-name>-remote package/rep)
        # pytest instead of python -m pytest
        run: |
          cd ./$project_name
          pip install pytest-cov
          # TODO: Fix this line so it will work with your modules
          # -s: to print statements
          pytest -s --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=$package_directory tests/ | tee pytest-coverage.txt
        #run: python -m unittest discover -s tests -p 'test_*.py'
        env:
          # Per Environment
          LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', env.env_name)] }}
          RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', env.env_name)] }}
          RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}
          # Per Microservice
          RDS_USERNAME: ${{ vars.RDS_USERNAME }}
          #AWS_ACCESS_KEY_ID: ${{ secrets[format('AWS_ACCESS_KEY_ID_{0}', env.env_name)] }}
          #AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', env.env_name)] }}
          #REGION: ${{ vars.AWS_DEFAULT_REGION }}
          #BUCKET_NAME: ${{ vars.BUCKET_NAME }}
          ENVIRONMENT: ${{ env.env_name }}

      # TODO: Please create pytest-coverage.txt and pytest.xml before this step
      - name: Pytest coverage comment
        id: coverageComment
        uses: MishaKav/pytest-coverage-comment@main
        with:
          pytest-coverage-path: ./pytest-coverage.txt
          junitxml-path: ./pytest.xml

      - name: Check the output coverage
        run: |
          echo "Coverage Percantage - ${{ steps.coverageComment.outputs.coverage }}"
          echo "Coverage Color - ${{ steps.coverageComment.outputs.color }}"
          #echo "Coverage Html - ${{ steps.coverageComment.outputs.coverageHtml }}"
          echo "Summary Report - ${{ steps.coverageComment.outputs.summaryReport }}"
          echo "Coverage Warnings - ${{ steps.coverageComment.outputs.warnings }}"
          echo "Coverage Errors - ${{ steps.coverageComment.outputs.errors }}"
          echo "Coverage Failures - ${{ steps.coverageComment.outputs.failures }}"
          echo "Coverage Skipped - ${{ steps.coverageComment.outputs.skipped }}"
          echo "Coverage Tests - ${{ steps.coverageComment.outputs.tests }}"
          echo "Coverage Time - ${{ steps.coverageComment.outputs.time }}"
          echo "Not Success Test Info - ${{ steps.coverageComment.outputs.notSuccessTestInfo }}"

      # TODO: I've added a few more steps in storage-local-python-package to support poetry. Do we need them?

      # TODO: Please make sure you have created pyproject.toml file in the project directory and not root directory and uncomment the bellow lines and the version is patch every time we run the GHA
      - name: Patch the version number in pyproject.toml
        run: |
          pip install poetry
          cd ./$project_name
          poetry version patch

      - name: Publish package
        uses: pypa/gh-action-pypi-publish@27b31702a0e7fc50959f5ad993c78deac1bdfc29
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
          packages_dir: ${{ env.project_name }}/dist
          # verbose: true  # For debugging


  # play1 Build, Test & Publish
  publish-database-infrastructure-local-python-package-play1:
    name: DBInfLocP()Play1 Build, Test & Publish
    # TODO When using this "if: startsWith(github.ref, 'refs/heads/bu-')", unfortunately, we can't use "on: pull_request:"
    if: startsWith(github.ref, 'refs/heads/bu-')
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    permissions:
      id-token: write # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write # To allow the Reuseable GitHub Action (i.e.  For publish_python_package.yml) to push the file created/updated by Sql2Code
      pull-requests: write # To allow the Reusable GitHub Action (i.e.  For publish_python_package.yml) to create a Pull Request (PR) following Sql2Code
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: database-infrastructure-local-python-package
      # Not needed for publish_ needed for run_
      #branch-name: dev
      repo-directory: database-infrastructure-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging can be changed to 1, debug, info ...
      #logger-minimum-severity: error
      #LOGGER_CONFIGURATION_JSON_PATH='contact-group-local-python-package\\.vscode\\logger_configuration.json'
      #TODO Only for debugging can be changed temporarily to true
      #logger-is-write-to-sql: false
