# This workflow will upload a Python Package using Twine when a release is created 
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python#publishing-to-package-registries

# This workflow uses actions that are not certified by GitHub.
# They are provided by a third party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: Build & Publish Logger Local Python Package play1

on:
  push:
    branches: [ "BU-*" ]
    # pull_request:
    # branches: [dev]

env:
  brand_name: Circlez
  environment_name: play1
  repo_name: logger-local-python-package

jobs:
  deploy:
    name: LogLocP()Play1 Old Build, Test, and Publish
    if: false
    runs-on: ubuntu-latest
    environment:
      name: play1 # If using $environment_name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      url: https://${{ env.environment_name }}.circ.zone
    strategy:
      #fail-fast: false
      matrix:
        python-version: [ "3.12" ]
        #poetry-version: ["2.1.6"]      
    steps:
      - uses: actions/checkout@v4.1.0

      - name: Setup Python
        uses: actions/setup-python@v4.7.1
        with:
          #python-version: "3.x"
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          cd ./$repo_name
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Lint with flake8
        run: |
          python -m pip install flake8 pytest
          # Stop the build if there are Python syntax errors or undefined names
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

      - name: Build package
        run: |
          cd ./$repo_name
          pip install build
          python -m build

      - name: Run Tests
        run: |
          pytest -s
        env:
          BRAND_NAME: ${{ env.brand_name }}
          ENVIRONMENT_NAME: ${{ env.environment_name }}

          LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', env.environment_name)] }}
          # this cause the tests to fail, so don't uncomment.
          # LOGGER_IS_WRITE_TO_SQL: false

          RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', env.environment_name)] }}
          RDS_USERNAME: ${{ vars.RDS_USERNAME }}
          RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}

          PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', env.environment_name)] }}
          PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', env.environment_name)] }}

      - name: Publish package
        uses: pypa/gh-action-pypi-publish@27b31702a0e7fc50959f5ad993c78deac1bdfc29
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
          packages_dir: ${{ env.repo_name }}/dist


  # play1 Build, Test, and Publish
  publish-logger-local-python-package-play1:
    name: LogLocP()Play1 Build, Test, and Publish
    if: startsWith(github.ref, 'refs/heads/bu-')
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: logger-local-python-package
      # Not needed for publish_ needed for run_
      #branch-name: dev
      repo-directory: logger-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging, should be commented
      #logger-minimum-severity: 1
      #logger-is-write-to-sql: 0


  run-database-mysql-local-python-package-play1-dev:
    name: DBMySQLLocalP(dev)Play1 Run
    #if: ${{ true }}
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-logger-local-python-package-play1
    strategy:
      # So we'll try to execute jobs in all environments even if one of them failed
      #fail-fast: false
      matrix:
        python-version: [ '3.x' ] # Latest 3.x version (Currently 3.12)
      ## TODO We didn't manage to have multiple environments
      #target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
      # TODO Is it mandatory?
      #permissions:
      #id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      #environment-name: ${{ matrix.target_environments }}
      #component-name: event
      repo-name: database-mysql-local-python-package
      branch-name: dev
      #repo-directory-name: profile_reddit_restapi_imp_local_python_package
      # TODO Misson _local_
      #repo-directory-name: profile_reddit_restapi_imp_python_package
      repo-directory: database-mysql-local-python-package
      #package-directory-name: database_mysql_local
      #sql2code-command-line: python .\.venv\Lib\site-packages\sql_to_code\sqltocode.py --schema database --table table_definition_table --output_path .\database_mysql_local\src\
      # At this point the GitHub Submodule brings only the dev branch, so we can use it only after we merge Sql2Code to dev
      #sql2code-command-line: "python sql2code/sqltocode_local_python/sql_to_code/src/sqltocode.py --schema database --table table_definition_table --output_path ./database_mysql_local/src"
      #is-run-local: true
      #is-rds-security-group: false
      #is-publish-to-test-pypi: false # Not working
      #is-publish-to-non-test-pypi is not defined in the referenced workflow.
      #is-publish-to-non-test-pypi: true
      # TODO We added this to make logger-local pass tests and publish
      logger-is-write-to-sql: true


  run-database-mysql-local-python-package-play1-1234:
    name: DBMySQLLocalP(1234)Play1 Run
    # Branch merged
    if: false
    #if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-logger-local-python-package-play1
    strategy:
      # So we'll try to execute jobs in all environments even if one of them failed
      #fail-fast: false
      matrix:
        python-version: [ '3.x' ] # Latest 3.x version (Currently 3.12)
      ## TODO We didn't manage to have multiple environments
      #target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
      # TODO Is it mandatory?
      #permissions:
      #id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      #environment-name: ${{ matrix.target_environments }}
      #component-name: event
      repo-name: database-mysql-local-python-package
      branch-name: BU-1234-Akiva-Skolnik
      #repo-directory-name: profile_reddit_restapi_imp_local_python_package
      # TODO Misson _local_
      #repo-directory-name: profile_reddit_restapi_imp_python_package
      repo-directory: database-mysql-local-python-package
      #package-directory-name: database_mysql_local
      #sql2code-command-line: python .\.venv\Lib\site-packages\sql_to_code\sqltocode.py --schema database --table table_definition_table --output_path .\database_mysql_local\src\
      # At this point the GitHub Submodule brings only the dev branch, so we can use it only after we merge Sql2Code to dev
      #sql2code-command-line: "python sql2code/sqltocode_local_python/sql_to_code/src/sqltocode.py --schema database --table table_definition_table --output_path ./database_mysql_local/src"
      #is-run-local: true
      #is-rds-security-group: false
      #is-publish-to-test-pypi: false # Not working
      #is-publish-to-non-test-pypi is not defined in the referenced workflow.
      #is-publish-to-non-test-pypi: true


  run-database-mysql-local-python-package-play1-2292:
    name: DBMySQLLocalP(2292)Play1 Run
    if: false
    # if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-logger-local-python-package-play1
    strategy:
      # So we'll try to execute jobs in all environments even if one of them failed
      #fail-fast: false
      matrix:
        python-version: [ '3.x' ] # Latest 3.x version (Currently 3.12)
      ## TODO We didn't manage to have multiple environments
      #target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
      # TODO Is it mandatory?
      #permissions:
      #id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      #environment-name: ${{ matrix.target_environments }}
      #component-name: event
      repo-name: database-mysql-local-python-package
      branch-name: BU-2292--add-order-by-to-methods-in-GenericCRUDML--Tal-Goodman-
      #repo-directory-name: profile_reddit_restapi_imp_local_python_package
      # TODO Misson _local_
      #repo-directory-name: profile_reddit_restapi_imp_python_package
      repo-directory: database-mysql-local-python-package
      #package-directory-name: database_mysql_local
      #sql2code-command-line: python .\.venv\Lib\site-packages\sql_to_code\sqltocode.py --schema database --table table_definition_table --output_path .\database_mysql_local\src\
      # At this point the GitHub Submodule brings only the dev branch, so we can use it only after we merge Sql2Code to dev
      #sql2code-command-line: "python sql2code/sqltocode_local_python/sql_to_code/src/sqltocode.py --schema database --table table_definition_table --output_path ./database_mysql_local/src"
      #is-run-local: true
      #is-rds-security-group: false
      #is-publish-to-test-pypi: false # Not working
      #is-publish-to-non-test-pypi is not defined in the referenced workflow.
      #is-publish-to-non-test-pypi: true


  run-dialog-workflow-local-python-package-play1-dev:
    name: DialogWFLocalP(dev)Play1 Build Publish
    needs: publish-logger-local-python-package-play1
    if: startsWith(github.ref, 'refs/heads/bu-')
      #if: false
      #strategy:
    #matrix:
    #target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    # TODO Is it mandatory?
    #permissions:
    #id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      #environment-name: ${{ matrix.target-environments }}
      repo-name: dialog-workflow-local-python-package
      branch-name: dev
      repo-directory: dialog-workflow-local-restapi-python-package


  run-dialog-workflow-local-python-package-play1-2314:
    name: DialogWFLocalP(2314)Play1 Run
    needs: publish-logger-local-python-package-play1
    #if: startsWith(github.ref, 'refs/heads/bu-')
    if: false
      #strategy:
    #matrix:
    #target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    # TODO Is it mandatory?
    #permissions:
    #id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      #environment-name: ${{ matrix.target-environments }}
      repo-name: dialog-workflow-local-python-package
      branch-name: BU-2314-add-present-form-action-Idan-Asis
      repo-directory: dialog-workflow-local-restapi-python-package


  # Run tests of other packages that are using this package (database-mysql-local-python) i.e. location-local-python-package
  deploy-dialog-workflow-restapi-python-serverless-com-play1-dev:
    name: DialogWFRestPSC(dev)Play1 Deploy
    #if: startsWith(github.ref, 'refs/heads/bu-')
    # As dev is outdated
    if: false
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/deploy_python_serverless_com.yml@main
    # TODO uncomment
    needs: run-dialog-workflow-local-python-package-play1-dev
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      #component-name: dialog-workflow
      repo-name: dialog-workflow-restapi-python-restapi-serverless-com
      branch-name: dev
      repo-directory: .
      #package-directory: .
      #is-run-local: true
      #is_rds_security_group: false


  deploy-dialog-workflow-restapi-python-serverless-com-play1-2019:
    name: DialogWFRestPSC(2019)Play1 Deploy
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: run-dialog-workflow-local-python-package-play1-2314
    # BU-2019 is still Red
    #if: false
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/deploy_python_serverless_com.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      #component-name: dialog-workflow
      repo-name: dialog-workflow-restapi-python-restapi-serverless-com
      branch-name: BU-2019-update-rest-api-in-dialog-workflow
      repo-directory: .
      # Relevant to run_ not relevant to deploy_
      #package-directory: .
      #is-run-local: true
      #is_rds_security_group: false      


  # Run tests of other packages that are using this package (database-mysql-local-python) i.e. location-local-python-package
  run-dialog-workflow-remote-restapi-typescript-package-play1-dev:
    name: DialogWFRemRestTS(dev)Play1
    if: startsWith(github.ref, 'refs/heads/bu-')
    # TODO uncomment
    #needs: deploy-dialog-workflow-restapi-python-serverless-com-play1-dev
    needs: deploy-dialog-workflow-restapi-python-serverless-com-play1-dev
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_typescript_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      #component-name: dialog-workflow
      repo-name: dialog-workflow-remote-restapi-typescript-package
      branch-name: dev
      repo-directory: dialog-workflow-remote-restapi-typescript-package
      is-run-local: true
      #is_rds_security_group: false


  # Run tests of other packages that are using this package (database-mysql-local-python) i.e. location-local-python-package
  run-dialog-workflow-remote-restapi-typescript-package-play1-2120:
    name: DialogWFRemRestTS(2120)Play1
    if: startsWith(github.ref, 'refs/heads/bu-')
    # TODO uncomment
    #needs: deploy-dialog-workflow-restapi-python-serverless-com-play1-dev
    needs: deploy-dialog-workflow-restapi-python-serverless-com-play1-2019
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_typescript_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      #component-name: dialog-workflow
      repo-name: dialog-workflow-remote-restapi-typescript-package
      branch-name: BU-2120-develop-dialog-workflow-remote
      repo-directory: dialog-workflow-remote-restapi-typescript-package
      is-run-local: true
      #is_rds_security_group: false


  # Running top-level Python executables
  publish-queue-worker-local-python-package-play1:
    name: QueueWorkerLocalP()Play1 Build, Test & Publish
    #if: ${{ true }}
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-logger-local-python-package-play1
    strategy:
      # So we'll try to execute jobs in all environments even if one of them failed
      #fail-fast: false
      matrix:
        python-version: [ '3.x' ] # Latest 3.x version (Currently 3.12)
      ## TODO We didn't manage to have multiple environments
      #target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    # TODO Is it mandatory?
    permissions:
      id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write
      pull-requests: write
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      #environment-name: ${{ matrix.target_environments }}
      #component-name: event
      repo-name: queue-worker-local-python-package
      branch-name: dev
      #repo_directory_name: profile_reddit_restapi_imp_local_python_package
      # TODO Misson _local_
      #repo_directory_name: profile_reddit_restapi_imp_python_package
      repo-directory: queue_worker_local_python_package
      #package_directory_name: database_mysql_local
      #sql2code_command_line: python .\.venv\Lib\site-packages\sql_to_code\sqltocode.py --schema database --table table_definition_table --output_path .\database_mysql_local\src\
      # At this point the GitHub Submodule brings only dev branch, so we can use it only after we merge Sql2Code to dev
      #sql2code_command_line: "python sql2code/sqltocode_local_python/sql_to_code/src/sqltocode.py --schema database --table table_definition_table --output_path ./database_mysql_local/src"
      #is_run_local: true
      #is_rds_security_group: false
      #is_publish_to_test_pypi: false # Not working
      # is_publish_to_non_test_pypi is not defined in the referenced workflow.
      #is_publish_to_non_test_pypi: true

  publish-smartlink-local-python-package-play1:
    name: SmartLinkLocalP()Play1 Build, Test & Publish
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-logger-local-python-package-play1
    strategy:
      # So we'll try to execute jobs in all environments even if one of them failed
      #fail-fast: false
      matrix:
        python-version: [ '3.x' ] # Latest 3.x version (Currently 3.12)
    permissions:
      id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write # TODO I added it to try allowing Sql2Code to update the repo. We might want to move the Sql2Code to a separate job (not step) and give permission to that job
      pull-requests: write # Allow Sql2Code to create Pull Request with the changes
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      #environment-name: ${{ matrix.target-environments }}
      repo-name: smartlink-local-python-package
      repo-directory: smartlink-local-python-package
      branch-name: dev


  ############## play1 sanity
  deploy-smartlink-restapi-python-serverless-com-play1-dev:
    name: SmartLinkRestPSC()Play1 Deploy
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-smartlink-local-python-package-play1
    #strategy:
    #matrix:
    # TODO We didn't manage to have multiple environments
    #target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/deploy_python_serverless_com.yml@main
    # If commenting the secrets: we need to provide token
    secrets: inherit
    permissions:
      pull-requests: write # This alone did not solve the "Resource not accessible by integration." error
      contents: write # https://github.com/MishaKav/pytest-coverage-comment/issues/68#issuecomment-1143862564
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      #environment-name: ${{ matrix.target-environments }}
      repo-name: smartlink-restapi-python-serverless-com
      branch-name: dev
      repo-directory: .
      #package-directory: .
      #is-run-remote: true


  # TODO Smartlink MySql user need access to logger_table
  run-smartlink-remote-restapi-python-package-play1-dev:
    name: SmartRemRestP(dev)Play1 Run
    # TODO works?
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: deploy-smartlink-restapi-python-serverless-com-play1-dev
      #if: ${{ true }}
    #strategy:
    #matrix:
    #target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    # TODO Is it mandatory?
    permissions:
      id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write
      pull-requests: write
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      #environment-name: ${{ matrix.target-environments }}
      repo-name: smartlink-remote-restapi-python-package
      repo-directory: smartlink-remote-restapi-python-package

  
  run-google-contact-local-python-package-play1-dev:
    name: GoogleContactLocP(dev)Play1 Run
    #if: startsWith(github.ref, 'refs/heads/bu-')
    if: false
    needs: publish-logger-local-python-package-play1
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      #component-name: google-contact
      repo-name: google-contact-local-python-package
      #branch-name: dev
      repo-directory: google-contact-local-python-package
      #TODO We should bring it back to dev ASAP
      #branch-name: BU-2206--develop-google-contact-local-python-package--Tal-Goodman-
      #package_directory_name: google-contact_local
      #is_rds_security_group: false
      #is_publish_to_non_test_pypi: true    


  run-contact-person-profile-csv-imp-local-python-package-play1-dev:
    name: ConPerProCsvImpLocalP(dev)Play1 Run
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-logger-local-python-package-play1
    strategy:
      fail-fast: false
      matrix:
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      repo-name: contact-person-profile-csv-imp-local-python-package
      branch-name: dev
      repo-directory: contact-person-profile-csv-imp-local


  run-profile-facebook-selenium-scraper-imp-local-python-package-play1-dev:
    name: ProfileFBSeleScraImpP(dev)Play1 Run
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-logger-local-python-package-play1
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: profile-facebook-selenium-scraper-imp-local-python-package
      # Not needed for publish_ needed for run_
      branch-name: dev
      repo-directory: profile-facebook-selenium-scraper-imp-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging, should be commented
      #logger-minimum-severity: 1


  run-text-block-local-python-package-play1-dev:
    name: TxtBlkLocP(dev)Play1 Run
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-logger-local-python-package-play1
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: text-block-local-python-package
      # Not needed for publish_ needed for run_
      branch-name: dev
      repo-directory: text-block-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging, should be commented
      #logger-minimum-severity: 1


  # Running the equivalent TypeScript 
  # As the logger is a very critical component, although there is no real dependency (except the logger schema), we decided to run also Logger TypeScript
  run-logger-local-typescript-package-play1-dev:
    name: LoggerLocTS(dev)Play1 Run
    #No need to add "needs" here
    #needs:
    #TODO It seems the "if:" on the dependabot branch is not working
    if: startsWith(github.ref, 'refs/heads/bu-')
    #if: ${{ startsWith(github.ref, 'refs/heads/bu-') || startsWith(github.ref, 'refs/heads/dependabot') }}
    #if: startsWith(github.ref, 'refs/heads/dependabot')
    needs: publish-logger-local-python-package-play1
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_typescript_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: logger-local-typescript-package
      branch-name: dev
      repo-directory: logger-local
      is-run-local: true
      #is_bump_version: false
      #is_rds_security_group: false


  # TODO This is not working probably due to a permission issue
  # We should deploy and not run, so the remote will use this code.
  deploy-logger-restapi-typescript-serverless-com-play1-dev:
    name: LoggerRestTSSC(dev)Play1 Build, Test, and Publish
    needs: run-logger-local-typescript-package-play1-dev
    if: startsWith(github.ref, 'refs/heads/bu-')
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/deploy_typescript_serverless_com.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand_name: Circlez
      environment-name: ${{ matrix.target_environments }}
      #token: ${{ secrets.GH_FINE_GRAINED_READ_REPO_CONTENTS_TOKEN }}
      repo-name: logger-restapi-typescript-serverless-com
      # `npm update` failed with a "Permission denied" error message, maybe because we didn't provide the exact branch-name
      #branch-name: BU-1471-When-environments-variables-in-Lambda-are-wrong-Logger-REST-API-returns-OK-while-is-suppose-to-return-error
      branch-name: dev
      #repo-directory: server-side
      repo-directory: logger-restapi-typescript-serverless-com
      #package-direcotry: RemoteLoggerServerSide-serverless
      #TODO Is it mandatory?
      package-directory: remote-logger-server-Side-serverless


  run-logger-remote-typescript-package-build-publish-play1-dev:
    name: LogRemoteTS(dev)Play1 Run
    needs: deploy-logger-restapi-typescript-serverless-com-play1-dev
    if: startsWith(github.ref, 'refs/heads/bu-')
    strategy:
      # To allow running multiple environments. Unfortunately, we need to change the other tasks to "if: always()"
      fail-fast: false
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_typescript_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      repo-name: logger-remote-typescript-package
      # We should not state the branch as we want the current branch
      branch-name: dev
      repo-directory: .
      #is_bump_version: false
      #is_rds_security_group: false
