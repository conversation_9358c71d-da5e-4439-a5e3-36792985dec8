name: Deploy data-source-local-python-package
on:
  push:
    branches: [ dev ]
  pull_request:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: dvlp1
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v3
        with:
          python-version: "3.x"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install build
      - name: Build package
        run: python -m build
      # poetry: command not found
      #- name: Increase the version number
      #run: poetry version patch
      - name: Test local with pytest
        run: pytest
        env:
          # Per Environment
          LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', env.env_name)] }}
          RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', env.env_name)] }}
          RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}
          # Per Microservice
          RDS_USERNAME: ${{ vars.RDS_USERNAME }}
          AWS_ACCESS_KEY_ID: ${{ secrets[format('AWS_ACCESS_KEY_ID_{0}', env.env_name)] }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', env.env_name)] }}
          REGION: ${{vars.AWS_DEFAULT_REGION}}
          BUCKET_NAME: ${{vars.BUCKET_NAME}}
          ENVIRONMENT: ${{ env.env_name }}

      # # TODO: Please create pytest-coverage.txt and pytest.xml
      # - name: Pytest coverage comment
      #   uses: MishaKav/pytest-coverage-comment@main
      #   with:
      #     pytest-coverage-path: ./pytest-coverage.txt
      #     junitxml-path: ./pytest.xml
      - name: Publish package
        uses: pypa/gh-action-pypi-publish@27b31702a0e7fc50959f5ad993c78deac1bdfc29
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}


  # play1 Build, Test & Publish
  publish-data-source-local-python-package-play1:
    name: DataSrcLocP()Play1 Build, Test & Publish
    if: startsWith(github.ref, 'refs/heads/bu-')
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    permissions:
      id-token: write # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write # To allow the Reuseable GitHub Action (i.e.  For publish_python_package.yml) to push the file created/updated by Sql2Code
      pull-requests: write # To allow the Reusable GitHub Action (i.e.  For publish_python_package.yml) to create a Pull Request (PR) following Sql2Code
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: data-source-local-python-package
      # Not needed for publish_ needed for run_
      #branch-name: dev
      repo-directory: data-source-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging can be changed to 1, debug, info ...
      #logger-minimum-severity: error
      #LOGGER_CONFIGURATION_JSON_PATH='contact-group-local-python-package\\.vscode\\logger_configuration.json'
      #TODO Only for debugging can be changed temporarely to true
      #logger-is-write-to-sql: false




  # play1 Build, Test & Publish
  publish-data-source-local-python-package-dvlp1-dev:
    name: DataSrcLocP()Dvlp1 Build, Test & Publish
    if: github.ref == 'refs/heads/dev'
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    permissions:
      id-token: write # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write # To allow the Reuseable GitHub Action (i.e.  For publish_python_package.yml) to push the file created/updated by Sql2Code
      pull-requests: write # To allow the Reusable GitHub Action (i.e.  For publish_python_package.yml) to create a Pull Request (PR) following Sql2Code
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: data-source-local-python-package
      # Not needed for publish_ needed for run_
      #branch-name: dev
      repo-directory: data-source-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging can be changed to 1, debug, info ...
      #logger-minimum-severity: error
      #LOGGER_CONFIGURATION_JSON_PATH='contact-group-local-python-package\\.vscode\\logger_configuration.json'
      #TODO Only for debugging can be changed temporarely to true
      #logger-is-write-to-sql: false
