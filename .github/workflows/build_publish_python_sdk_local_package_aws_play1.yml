name: Deploy Python SDK Local Package to PyPI.org AWS play1
on:
  # When we comment "on: push" Sanity Tests are not running
  push:
    branches: [ "BU-*" ]
  #pull_request:
    #branches: [ dev ]

env:
  # TODO: If we don't have separate files for each environment, we should update the environment on a regular basis in two places in this file (env: and environment:)
  brand_name: Circlez
  environment_name: play1
  repo_name: python-sdk-remote-python-package
  package_name: python-sdk  # TODO Do we use it?
  package_directory: python_sdk_remote

jobs:
  python-sdk-remote-python-old-build-publish:
    name: PythonSdkRemP Old Build & Publish
    if: false
    runs-on: ubuntu-latest
    environment:
      name: play1 # If using $environment_name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      url: https://${{ env.environment_name }}.circ.zone
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v3
        with:
          python-version: '3.x'

      - name: Install dependencies
        run: |
          cd ./$repo_name
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Lint with flake8
        run: |
          python -m pip install flake8 pytest
          # stop the build if there are Python syntax errors or undefined names
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics     

      - name: Build package
        run: |
          pip install build
          cd ./$repo_name
          python -m build

      #run: |
      #    pytest
      # TODO: Please write Unit-Test in /<project-name>/tests directory
      # TODO: Please create <project-name>-graphql/restapi-python-package in a separate repo to test the published package
      - name: Test locally with pytest (Test the published package should be done by <project-name>-graphql/restapi Unit-Test)
        # pytest instead of python -m pytest
        run: |
          cd ./$project_name
          pip install pytest-cov
          pytest -s --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov-report=xml:coverage.xml --cov=. | tee pytest-coverage.txt; test ${PIPESTATUS[0]} -eq 0
        #run: python -m unittest discover -s tests -p 'test_*.py'
        env:
          # TODO: Please comment and then delete all variables not being used.
          # Per Environment
          LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', env.environment_name)] }}
          RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', env.environment_name)] }}
          RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}
          PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', env.environment_name)] }}
          PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', env.environment_name)] }}
          RDS_USERNAME: ${{ vars.RDS_USERNAME }}
          AWS_ACCESS_KEY_ID: ${{ secrets[format('AWS_ACCESS_KEY_ID_{0}', env.environment_name)] }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', env.environment_name)] }}
          REGION: ${{ vars.AWS_DEFAULT_REGION }}
          BUCKET_NAME: ${{ vars.BUCKET_NAME }}
          BRAND_NAME: ${{ env.brand_name }}
          ENVIRONMENT_NAME: ${{ env.environment_name }}

      # TODO: Please create pytest-coverage.txt and pytest.xml before this step
      - name: Pytest coverage comment
        id: coverageComment
        uses: MishaKav/pytest-coverage-comment@main
        with:
          pytest-coverage-path: ./pytest-coverage.txt
          pytest-xml-coverage-path: ./coverage.xml
          junitxml-path: ./pytest.xml
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Check the output coverage
        run: |
          echo "Coverage Percantage - ${{ steps.coverageComment.outputs.coverage }}"
          echo "Coverage Color - ${{ steps.coverageComment.outputs.color }}"
          echo "Coverage Warnings - ${{ steps.coverageComment.outputs.warnings }}"
          echo "Coverage Errors - ${{ steps.coverageComment.outputs.errors }}"
          echo "Coverage Failures - ${{ steps.coverageComment.outputs.failures }}"
          echo "Coverage Skipped - ${{ steps.coverageComment.outputs.skipped }}"
          echo "Coverage Tests - ${{ steps.coverageComment.outputs.tests }}"
          echo "Coverage Time - ${{ steps.coverageComment.outputs.time }}"
          echo "Not Success Test Info - ${{ steps.coverageComment.outputs.notSuccessTestInfo }}"

      # TODO: I've added a few more steps in storage-local-python-package to support poetry. Do we need them?

      # TODO: Please make sure you have created pyproject.toml file in the project directory and not the root directory and uncomment the bellow lines and the version is patch every time we run the GHA
      - name: Patch the version number in pyproject.toml
        run: |
          pip install poetry
          cd ./$repo_name
          poetry version patch

      - name: Publish package
        uses: pypa/gh-action-pypi-publish@27b31702a0e7fc50959f5ad993c78deac1bdfc29
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
          packages_dir: ${{ env.repo_name }}/dist
          # verbose: true

  publish-python-sdk-remote-python-package-play1:
    name: PythonSdkRemP()Play1 Build, Test, and Publish
    #if: startsWith(github.ref, 'refs/heads/bu-')
    # When debuging "generate dependencies"
    #if: false
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    # TODO Is it mandatory?
    permissions:
      id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write
      pull-requests: write
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      #component-name: event
      repo-name: python-sdk-remote-python-package
      #branch_name: ${{ github.ref }}
      #repo_directory_name: profile_reddit_restapi_imp_local_python_package
      repo-directory: python-sdk-remote-python-package
      #package_directory_name: database_mysql_local
      #sql2code_command_line: python .\.venv\Lib\site-packages\sql_to_code\sqltocode.py --schema database --table table_definition_table --output_path .\database_mysql_local\src\
      # At this point the GitHub Submodule brings only dev branch, so we can use it only after we merge Sql2Code to dev
      #sql2code_command_line: "python sql2code/sqltocode_local_python/sql_to_code/src/sqltocode.py --schema database --table table_definition_table --output_path ./database_mysql_local/src"
      #is_run_remote: true
      #is_rds_security_group: false
      #is_publish_to_test_pypi: false # Not working
      #is_publish_to_non_test_pypi: true
      # TODO Change it to false or comment
      #logger-is-write-to-sql: true


  generate-dependency-json-and-graphs-play1:
    name: generate-dependency-json-and-graphs-play1
    # Reduce costs FinOps
    if: false
    runs-on: ubuntu-latest
    environment: play1
    #needs: publish-python-sdk-remote-python-package-play1
    steps:
      - uses: actions/checkout@v3
      # This step is independent and can be executed in a separate job
      - name: Generate dependency JSON and 7 dependencies tree graphs
        run: |
          echo "List of reports"
          # TODO repo_name -> repo_directory
          cd ./$repo_name/$package_directory
          echo "Running BeautifulSoup4"
          pip install BeautifulSoup4 requests importlib-metadata networkx matplotlib scipy
          mkdir -p utils_output
          cd utils_putput
          echo "Running get_dependencies"
          python -m utils.get_dependencies

      # https://github.com/marketplace/actions/create-pull-request
      # TODO Shall we use PAT or GITHUB_TOKEN?
      # TODO update parameters in 'with'
      - name: Create Pull Request (for sql2code and other changes in the code)
        uses: peter-evans/create-pull-request@v6

          # Sanity Tests
          ###
          # "action-items-local",
          # "api-management-local",
          # "contact-group-local",
          # "contact-persons-local",
          # "database-infrastructure-local",
          # "database-mysql-local",
          # "dialog-workflow-local",
          # "email-message-aws-ses-local",
          # "event-external-local",
          # "google-contact-local",
          # "group-profile-remote",
          # "group-remote",
          # "location-local",
          # "logger-local",
          # "organizations-local",
          # "organization-profile-local",
          # "people-local",
          # "profile-local",
          # "profile-reddit-restapi-imp-local",
          # "smartlink-remote-restapi",
          # "sms-message-aws-sns-local",
          # "star-local",
          # "storage-local",
          # "storage-remote",
          # "user-context-remote",
          # "user-external-local",
          # "user-local",
          # "whataspp-inforu-local",
          # "whataspp-message-inforu-local",
        # "whatsapp-message-vonage-local"
  ###

  run-user-context-remote-python-package-play1-dev:
    name: UserConRemP(dev)Play1 Run
    #if: false
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-python-sdk-remote-python-package-play1
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: user-context-remote-python-package
      branch-name: dev
      # TODO Move the requirments.txt from the root directory
      repo-directory: user-context-remote-python-package
      package-directory: user_context_remote


  run-google-contact-local-python-package-play1-dev:
    name: GooConLocP(dev)Play1 Run
    # 1. As it is not ready, 2. As it need manual authentication
    if: false
    #if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-python-sdk-remote-python-package-play1
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: google-contact-local-python-package
      branch-name: dev
      # TODO Move the requirments.txt from the root directory
      repo-directory: .
      package-directory: google-contacts-fetcher


  # play1 Build, Test, and Publish
  run-profile-facebook-selenium-scraper-imp-local-python-package-play1-dev:
    name: ProfileFBSeleScraImpLocalP(dev)Play1 Run
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-python-sdk-remote-python-package-play1
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: profile-facebook-selenium-scraper-imp-local-python-package
      # Not needed for publish_ needed for run_
      #branch-name: dev
      repo-directory: profile-facebook-selenium-scraper-imp-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: <package-directory>
      #TODO Only for debugging, should be commented
      #logger-minimum-severity: 1


  run-group-profile-remote-restapi-python-package-play1-dev:
    name: GroupProfileRemP(dev)Play1 Run
    #if: ${{ true }}
    if: startsWith(github.ref, 'refs/heads/bu-')
    # TODO Due to a bug in group-profile-remote-python and TypeScript
    #if: false
    needs: publish-python-sdk-remote-python-package-play1
    strategy:
      # So we'll try to execute jobs in all environments even if one of them failed
      fail-fast: false
      #matrix:
      ## TODO We didn't manage to have multiple environments
      #target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    # TODO Is it mandatory?
    permissions:
      id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write
      pull-requests: write
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      #environment-name: ${{ matrix.target_environments }}
      #component-name: event
      repo-name: group-profile-remote-restapi-python-package
      #branch_name: ${{ github.ref }}
      #repo_directory_name: profile_reddit_restapi_imp_local_python_package
      # TODO Misson _local_
      #repo_directory_name: profile_reddit_restapi_imp_python_package
      repo-directory: group-profile-remote-python-package
      #package_directory_name: database_mysql_local
      #sql2code_command_line: python .\.venv\Lib\site-packages\sql_to_code\sqltocode.py --schema database --table table_definition_table --output_path .\database_mysql_local\src\
      # At this point the GitHub Submodule brings only dev branch, so we can use it only after we merge Sql2Code to dev
      #sql2code_command_line: "python sql2code/sqltocode_local_python/sql_to_code/src/sqltocode.py --schema database --table table_definition_table --output_path ./database_mysql_local/src"
      #is_run_local: true
      #is_rds_security_group: false
      #is_publish_to_test_pypi: false # Not working
      # is_publish_to_non_test_pypi is not defined in the referenced workflow.
      #is_publish_to_non_test_pypi: true
      is-run-remote: true


  # play1 Build Publish, and Test
  run-contact-person-profile-csv-imp-local-python-package-play1-dev:
    name: ContPerProCsvImpLocalP(dev)Play1 Run
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-python-sdk-remote-python-package-play1
    strategy:
      fail-fast: false
      matrix:
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      repo-name: contact-person-profile-csv-imp-local-python-package
      repo-directory: contact-person-profile-csv-imp-local


  # Make sure sql2code is working using the latest version of python-sdk-remote-python-package
  run-sql2code-local-python-package-play1-dev:
    name: Sql2CodeLocalP(dev)Play1 Run
    if: startsWith(github.ref, 'refs/heads/bu-')
    needs: publish-python-sdk-remote-python-package-play1
    strategy:
      fail-fast: false
      matrix:
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/run_python_package.yml@main
    secrets: inherit
    with:
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      repo-name: sql2code-local-python-package
      repo-directory: sql2code-local-python-package


# TODO Which packages are using this package?
