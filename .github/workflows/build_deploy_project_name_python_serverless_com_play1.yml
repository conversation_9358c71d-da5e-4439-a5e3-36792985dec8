# This workflow will run tests using node and then publish a package to GitHub Packages when a release is created
# For more information see: https://docs.github.com/en/actions/publishing-packages/publishing-nodejs-packages

# TODO: Please change the <project-name> to the folder/project name everywhere and delete this line
name: Build & Publish <repo-name> play1

on:
  # TODO: Please make sure your code complies with our Definition of Done (DoD), `npm prepare`, `npm test`, tests cover 95% of the code, resolve all lint warnings, GitHub Action is Green in play1 before you create a Pull Request to the dev branch (dvlp1 Environment) 
  push:
    branches: [ "BU-*" ]
  #pull_request: 
    #branches: [ dev ]
      #- dev
  #workflow_dispatch:

#env:
  #brand_name: Circlez
  #component-name: event
  #environment_name: play1
  ## TODO: <repo_name> should be the folder/directory of the project. Same as the repo name but with an underscore. The root folder should have only .github directory and the project directory.
  #repo_name: <project-name>
  # TODO: repo_name same as repo name, but with an underscore instead of hyphen/minus/dash as the main directory/folder
  # TODO: Make sure the /src and /tests directories are under the directory with the same name as the repo called the project directory i.e. /<project-name>/src We should create/update the directory name to be the same as the repo name i.e. /time_local_python_package (with an underscore). In the root directory, we should have only .gitignore
  #repo_name: event-remote-typescript-package # TODO Should change the directory to event-remote-restapi-typescript-package
  # package name is used in Python at this point it is not in use in TypeScript
  #package_name: <package-directory-with-underlines> # TODO i.e. unified_json_api or unified_json_api_local

  # In the case of remote it should be always false or better delete the four paragraphs using it
  #is_rds_security_group: false

jobs:
  #reusable_workflow_job:
    # Only if we have steps
    #runs-on: ubuntu-latest
    #environment: production
    #strategy:
      #matrix:
        #target: [dev, stage, prod]
    #steps:
    #- uses: actions/labeler@v4
      #with:
        #repo-token: ${{ secrets.envPAT }}
        #configuration-path: ${{ inputs.config-path }}
  deploy-python-serverless-com-<component-name>-play1:
    name: Call Build Publish TypeScript
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/deploy_python_serverless_com.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      component-name: <component-name>
      brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      repo-name: <repo-name>
      repo-directory: <repo-name>
      #is-rds-security-group: false
      #SQL-USERNAME: ${{ vars.RDS_USERNAME }} # How can we avoid sending it
      #TODO Only for debugging, should be commented
      #logger-minimum-severity: 1


  process_results:
    name: Process the result of the job
    runs-on: ubuntu-latest
    needs: call-build-publish
    steps:
    - run: |
        echo $repo_name
        echo ${{ needs.call-build-publish.outputs.firstword }} ${{ needs.call-build-publish.outputs.secondword }}
