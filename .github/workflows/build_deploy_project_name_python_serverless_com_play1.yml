# This workflow will run tests using node and then publish a package to GitHub Packages when a release is created
# For more information see: https://docs.github.com/en/actions/publishing-packages/publishing-nodejs-packages

# TODO: Please change the <project-name> to the folder/project name everywhere and delete this line
name: Build & Publish Event Remote RESTAPI TypeScript Node.js npm Package to GitHub Private Registry (GPR) play1

on:
  # TODO: Please make sure your code complies with our Definition of Done (DoD), `npm prepare`, `npm test`, tests cover 95% of the code, resolve all lint warnings, GitHub Action is Green in play1 before you create a Pull Request to the dev branch (dvlp1 Environment) 
  push:
    branches: [ "BU-*", dev ]
      #  pull_request:
      #    branches: [ dev ]
    #- dev
  # workflow_dispatch:

jobs:
#  reusable_workflow_job:
#    # Only if we have steps
#    runs-on: ubuntu-latest
#    environment: production
#    strategy:
#      matrix:
#        target: [dev, stage, prod]
#    steps:
#    - uses: actions/labeler@v4
#      with:
#        repo-token: ${{ secrets.envPAT }}
#        configuration-path: ${{ inputs.config-path }}
  deploy-google-account-restapi-python-restapi-serverless-com-play1:
    name: GglAccEest()Play1 Deploy
    if: startsWith(github.ref, 'refs/heads/bu-')
    strategy:
      matrix:
        # TODO We didn't manage to have multiple environments
        target_environments: [ play1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/deploy_python_serverless_com.yml@main
    secrets: inherit
    with:
      # TODO We didn't manage to use the env: directly
      #brand-name: Circlez
      environment-name: ${{ matrix.target_environments }}
      #component-name: database-mysql
      repo-name: google-account-restapi-python-serverless-com
      repo-directory: .
      #package-directory_name: .
      #is-run-remote: false
      #is-rds-security_group: false
      #TODO Only for debugging, should be commented
      #logger-minimum-severity: 1


  google-account-remote-restapi-python-package TODO We should develop and run google-account-restapi-remote
    run: -
      echo "TODO We should develop and run google-account-restapi-remote"

- name: google-account-remote-restapi-typescript-package TODO We should develop and run google-account-restapi-remote