name: Build Publish data-source-local-python-package to PyPI.org play1
on:
  push:
    branches: [ "BU-*" ]
  # pull_request:
  #   branches: [ dev ]

env:
  brand_name: Circlez
  environment_name: play1
  repo_name: data-source-local-python-package

  #permissions:
  #contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment:
      name: play1 # If using $environment_name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      url: https://${{ env.environment_name }}.circ.zone

    strategy:
      #fail-fast: false
      matrix:
        #python-version: ["3.11"]
        #poetry-version: ["2.1.6"]
        python-version: [ '3.x' ] # Latest 3.x version
    steps:
      - uses: actions/checkout@main # v4.1.0 # https://github.com/actions/checkout

      - name: Setup Python
        uses: actions/setup-python@main # v4.7.1 # https://github.com/actions/setup-python
        with:
          #python-version: '3.x'
          python-version: ${{ matrix.python-version }}

      # needs requirements.txt
      - name: Install dependencies
        run: |
          echo "Install dependencies"
          cd ./$repo_name
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          #if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

      - name: Lint with flake8
        run: |
          python -m pip install flake8 pytest
          # Stops the build if there are Python syntax errors or undefined names
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics        

      - name: Build package
        run: |
          cd ./$repo_name
          pip install build
          python -m build

      #run: |
      #    pytest
      - name: Test locally with pytest (Test the published package should be done by the data_source_local Unit-Test)
        # pytest instead of python -m pytest
        run: |
          cd ./$repo_name
          pip install pytest-cov
          pytest --junitxml=pytest.xml --cov-report=term-missing:skip-covered --cov=app data_source_local/tests/ | tee pytest-coverage.txt
        #run: python -m unittest discover -s tests -p 'test_*.py'
        env:
          BRAND_NAME: ${{ env.brand_name }}
          ENVIRONMENT_NAME: ${{ env.environment_name }}

          PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', env.environment_name)] }}
          PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', env.environment_name)] }}

          LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', env.environment_name)] }}

          RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', env.environment_name)] }}
          RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}
          RDS_USERNAME: ${{ vars.RDS_USERNAME }}

          AWS_ACCESS_KEY_ID: ${{ secrets[format('AWS_ACCESS_KEY_ID_{0}', env.environment_name)] }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', env.environment_name)] }}

          REGION: ${{ vars.AWS_DEFAULT_REGION }}
          BUCKET_NAME: ${{ vars.BUCKET_NAME }}

      - name: Pytest coverage comment
        uses: MishaKav/pytest-coverage-comment@main
        with:
          pytest-coverage-path: ./pytest-coverage.txt
          junitxml-path: ./pytest.xml

      - name: Check the output coverage
        run: |
          echo "Coverage Percentage - ${{ steps.coverageComment.outputs.coverage }}"
          echo "Coverage Color - ${{ steps.coverageComment.outputs.color }}"
          echo "Coverage Html - ${{ steps.coverageComment.outputs.coverageHtml }}"
          echo "Summary Report - ${{ steps.coverageComment.outputs.summaryReport }}"
          echo "Coverage Warnings - ${{ steps.coverageComment.outputs.warnings }}"
          echo "Coverage Errors - ${{ steps.coverageComment.outputs.errors }}"
          echo "Coverage Failures - ${{ steps.coverageComment.outputs.failures }}"
          echo "Coverage Skipped - ${{ steps.coverageComment.outputs.skipped }}"
          echo "Coverage Tests - ${{ steps.coverageComment.outputs.tests }}"
          echo "Coverage Time - ${{ steps.coverageComment.outputs.time }}"
          echo "Not Success Test Info - ${{ steps.coverageComment.outputs.notSuccessTestInfo }}"

      - name: Patch the version number in pyproject.toml
        run: |
          pip install poetry
          cd ./$repo_name
          poetry version patch

      - name: Publish package
        # Uses setup.py and the name in this GHA
        uses: pypa/gh-action-pypi-publish@27b31702a0e7fc50959f5ad993c78deac1bdfc29
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
          packages_dir: ${{ env.repo_name }}/dist
          # verbose: true  # For debugging
