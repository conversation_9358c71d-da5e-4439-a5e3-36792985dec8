name: Build Publish External User Local Python Package to PyPI.org dvlp1

on:
  push:
    branches: [ dev ]
  #pull_request:
    #branches: [ dev ]

env:
  brand_name: Circlez
  environment_name: dvlp1
  repo_name: user-external-local-python-package

jobs:
  deploy:
    if: false
    runs-on: ubuntu-latest
    environment:
      name: dvlp1 # If using $environment_name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      url: https://${{ env.environment_name }}.circ.zone
    steps:
      - uses: actions/checkout@v4.1.0 # https://github.com/actions/checkout

      - name: Set up Python
        uses: actions/setup-python@v4.7.1 # https://github.com/actions/setup-python
        with:
          python-version: "3.x"

      - name: Install dependencies
        run: |
          cd ./$repo_name
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Lint with flake8
        run: |
          python -m pip install flake8 pytest
          # stop the build if there are Python syntax errors or undefined names
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics   

      - name: Build package
        run: |
          cd ./$repo_name
          pip install build
          python -m build

      - name: Run Tests
        run: pytest
        env:
          BRAND_NAME: ${{ env.brand_name }}
          ENVIRONMENT_NAME: ${{ env.environment_name }}

          LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', env.environment_name)] }}

          RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', env.environment_name)] }}
          RDS_USERNAME: ${{ vars.RDS_USERNAME }}
          RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}

          PRODUCT_USER_IDENTIFIER: ${{ vars[format('PRODUCT_USER_IDENTIFIER_{0}', env.environment_name)] }}
          PRODUCT_PASSWORD: ${{ secrets[format('PRODUCT_PASSWORD_{0}', env.environment_name)] }}

      - name: Pytest coverage comment
        uses: MishaKav/pytest-coverage-comment@main
        with:
          pytest-coverage-path: ./pytest-coverage.txt
          junitxml-path: ./pytest.xml

      # TODO: Please make sure you have created pyproject.toml file and uncomment the bellow lines and the version is patch every time we run the GHA
      - name: Patch the version number in pyproject.toml
        run: |
          pip install poetry
          cd ./$repo_name
          poetry version patch

      - name: Publish package
        uses: pypa/gh-action-pypi-publish@27b31702a0e7fc50959f5ad993c78deac1bdfc29
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
          packages_dir: ${{ env.repo_name }}/dist


  # Dvlp1 Build, Test & Publish
  publish-user-external-main-local-python-package-dvlp1-dev:
    name: UserExtLocP(dev)Dvlp1 Build, Test & Publish
    # TODO When using this "if: startsWith(github.ref, 'refs/heads/bu-')", unfortunetly we can't use "on: pull_request:"
    if: github.ref=='refs/heads/dev'
    strategy:
      fail-fast: false
      matrix:
        target-environments: [ dvlp1 ] # [ play1, dvlp1, prod1 ]
    uses: circles-zone/github-workflows/.github/workflows/publish_python_package.yml@main
    permissions:
      id-token: write # IMPORTANT: this permission is mandatory for trusted publishing https://github.com/pypa/gh-action-pypi-publish
      contents: write # To allow the Reuseable GitHub Action (i.e.  For publish_python_package.yml) to push the file created/updated by Sql2Code
      pull-requests: write # To allow the Reusable GitHub Action (i.e.  For publish_python_package.yml) to create a Pull Request (PR) following Sql2Code
    secrets: inherit
    with:
      #brand-name: Circlez
      environment-name: ${{ matrix.target-environments }}
      repo-name: user-external-main-local-python-package
      # Not needed for publish_ needed for run_
      #branch-name: dev
      repo-directory: user-external-main-local-python-package
      # Not needed for publish_ needed for run_
      #package-directory: user_external_local
      #TODO Only for debugging can be changed to 1, debug, info ...
      #logger-minimum-severity: error
      #LOGGER_CONFIGURATION_JSON_PATH='contact-group-local-python-package\\.vscode\\logger_configuration.json'
      #TODO Only for debugging can be changed temporarely to true
      #logger-is-write-to-sql: false
