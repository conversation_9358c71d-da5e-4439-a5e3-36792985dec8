# TODO: Replace data-source-local-python-package everywhere
name: Build Publish data-source-local-python-package Python Package to PyPI.org dvlp1
on:
  push:
    branches: [ dev ]
  pull_request:
    branches: [ main ]

env:
  env_name: dvlp1
  # TODO: make sure all our code is in a project directory and not in the root. Under the project directory we have src directory i.e. /data-source-local-python-package/src We should update the directory name here. In the root directory we should have only .gitignore
  project_name: . # TODO: Should be data-source-local-python-package

  #permissions:
  #contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment:
      name: dvlp1 # If using $env_name, GitHub Environment Secrets are not deployed in Lambda Function Environment
      url: https://${{ env.env_name }}.circ.zone

        #strategy:
        #fail-fast: false
        #matrix:
      #python-version: [ "3.11" ]

    steps:
      - uses: actions/checkout@v3.5.3 # v3 -> v3.5.3

      - name: Setup Python
        uses: actions/setup-python@main # v4.7.0
        with:
          python-version: '3.x'
          #python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          #if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

      - name: Lint with flake8
        run: |
          python -m pip install flake8 pytest
          # stop the build if there are Python syntax errors or undefined names
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics        

      - name: Build package
        run: |
          pip install build
          python -m build

      #run: |
      #    pytest
      # TODO: Please write Unit-Test
      # TODO: Please create data-source-local-python-package-remote-typescript-package in a seperate repo to test the published package from TypeScript
      - name: Test local with pytest (test of published package should be by data-source-local-python-package-remote package/rep)
        run: pytest # Instead of python -m pytest
        #run: python -m unittest discover -s tests -p 'test_*.py'
        env:
          # Per Environment
          LOGZIO_TOKEN: ${{ secrets[format('LOGZIO_TOKEN_{0}', env.env_name)] }}
          RDS_HOSTNAME: ${{ vars[format('RDS_HOSTNAME_{0}', env.env_name)] }}
          RDS_PASSWORD: ${{ secrets.RDS_PASSWORD }}
          # Per Microservice
          RDS_USERNAME: ${{ vars.RDS_USERNAME }}
          AWS_ACCESS_KEY_ID: ${{ secrets[format('AWS_ACCESS_KEY_ID_{0}', env.env_name)] }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets[format('AWS_SECRET_ACCESS_KEY_{0}', env.env_name)] }}
          REGION: ${{vars.AWS_DEFAULT_REGION}}
          BUCKET_NAME: ${{vars.BUCKET_NAME}}
          ENVIRONMENT: ${{ env.env_name }}

      # TODO: Please create pytest-coverage.txt and pytest.xml
      - name: Pytest coverage comment
        uses: MishaKav/pytest-coverage-comment@main
        with:
          pytest-coverage-path: ./pytest-coverage.txt
          junitxml-path: ./pytest.xml

      # TODO: I've added a few more steps in storage-local-python-package to support poetry. Do we need them?

      # TODO: Please make sure you have created pyproject.toml file in the project directory and not root directory and uncomment the bellow lines and the version is patch every time we run the GHA
      - name: Patch the version number in pyproject.toml
        run: |
          pip install poetry
          cd ./$project_name
          poetry version patch

      - name: Publish package
        uses: pypa/gh-action-pypi-publish@27b31702a0e7fc50959f5ad993c78deac1bdfc29
        with:
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
          # verbose: true  # For debugging
