# python-serverless-com-backend-template
For serverless.com GraphQL/REST-API

# How to create Python serverless.com (GraphQL/REST-API)?
<h2>Using the terminal:</h2><br>
npm install -g serverless<br>
cd \<project-name\><br>
npm i<br>
pip install -r requirements.txt<br>
<h2>Manualy:</h2><br>
Delete the .REMOVE_POST_FIX from .env.REMOVE_POST_FIX<br>
Under package-lock.json rename "name" to the project name<br>

# serverless.yml<br>
Under serverless.yml change "example-project" to the project name<br>
Under serverless.yml change "example_lambda" to your lambda name<br>
Name of the AWS Lambda Function xxx-dvlp1-functionName<br>
Can't be too long (overall length should be 63 bytes including suffix)<br>
        xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx<br>

# How to run api localy?
serverless offline start<br>

# Notes:
After every pip install, manualy add the package to reqirements.txt<br>
NEVER USER pip freeze > requirements.txt<br>

# Working with VS Code
Please make sure you push to the repo launch.json fie that enables to run and debug the code<br> 

# TODO:
Open API Documentation<br>
Import API Service package<br>
Add Dtos in a Dtos folder<br>
Add logger<br>
Add CircStorage<br>
Add ServiceResponse package class<br>
Add SqlConnection package class<br>

# When you took care of all the TODOs in the repo, your Feature Branch GitHub Actions Workflow is Green without warnings, all tests are running in GHA, code is documented, README.md is clean and self explained, test coverage is above 90% and all your lines are covered with Unit-Tests, you can filter and analyse your records in Logz.io, pull dev to your Feature Branch, create remote-package, add the remote-package as the last step in this GHA to test the REST-API/GraphQL only then create Pull Request to dev.
