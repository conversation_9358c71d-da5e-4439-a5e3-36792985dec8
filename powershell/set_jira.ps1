# ./tal-circlez.ai/powershell/set_jira.ps1
# /repos/circlez/tal-circlez.ai/powershell/set_jira.ps1

# TODO Should be based on directory to support multiple instances/companies

Write-Host "Setting Jira environment variables..."

# Circlez.ai https://circles-zone.atlassian.net/jira/your-work
# System settings- General configuration- 
# https://id.atlassian.com/manage-profile/security/api-tokens
# $env:JIRA_URL = "https://circles-zone.atlassian.net/jira"
$env:JIRA_URL = "https://circles-zone.atlassian.net"
$env:JIRA_TOKEN = "ATATT3xFfGF08engjpxqawekicaBprkD2BkXg01EKO4oFMjbjEy4Xb0ezlJi9XmUsQR5opsw2iFfu48bio0cAyZek8puN9vYmQpz0gvG3xWAEzwLe-CBpVZMuGeEKaGGAi6kvZuwROcXSif-5Nizuit099ROBWk-7bTVhbACCr-lcPn3huAFZTw=4AD7403F"
$env:JIRA_PROJECT = "BU"

# Pause
