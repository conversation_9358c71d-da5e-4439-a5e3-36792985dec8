# ./tal-circlez.ai/powershell/set_jira.ps1
# /repos/circlez/tal-circlez.ai/powershell/set_jira.ps1

# TODO Should be based on directory to support multiple instances/companies

Write-Host "Setting Git environment variables..."

$env:GITHUB_TOKEN = "*********************************************************************************************"
$env:GITHUB_TOKEN = "****************************************"

# Circlez.ai https://circles-zone.atlassian.net/jira/your-work
# System settings- General configuration- 
# https://id.atlassian.com/manage-profile/security/api-tokens
# $env:JIRA_URL = "https://circles-zone.atlassian.net/jira"
#$env:GIT_REPOS_DIRECTORY = "~/repos/circlez"

# Pause
