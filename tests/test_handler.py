import json  # Add this import
from src.handler import google_account_get_code_lambda_handler
from python_sdk_local.service_response import ServiceResponse


def test_missing_code_parameter_simplified():
    """
    Tests the handler when the 'code' parameter is missing.
    This is a simplified test that does not use mocks.
    """
    request_parameters = {"state": None}

    inner_dict_for_body = ServiceResponse(
        message_internal={"message": "Access token is None"},
        message_external={
            "en": "Access token is None",
        },
        is_success=False,
        data={},
    ).to_http_response()

    # Construct the expected final HTTP response structure
    expected_response_dict = {
        # TODO please use http response enumeration everywhrere
        "statusCode": 200,
        "headers": {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
        },
        "body": json.dumps(inner_dict_for_body)
    }

    actual_response_dict = google_account_get_code_lambda_handler(
        request_parameters, None
    )

    assert actual_response_dict == expected_response_dict
