# TODO: Preferable the same name as the name of the repo, but it is too long

# TODO We need to make decision https://stackoverflow.com/questions/********/how-to-remove-stage-from-urls-for-aws-lambda-functions-serverless-framework

# TODO Can we move this file into the repo-directory?

# For example:
#   service: 'group-restapi-py-slc'
#   service: 'group-graphql-py-slc'
# Can't be too long (overall length should be 63 bytes including suffix)
#        xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx 
service: 'google-account-restapi-py-slc'

# Should be correlated to package.json version
frameworkVersion: '4' # https://github.com/serverless/serverless/releases 3.35.2 -> 3.38.0
# useDotenv: true
provider:
  name: aws
  # For local run python3.11 is not working, so we need to use python3.9 
  # TODO we should fix it, may be upgrade to Serverless 4.0 and/or Python 3.12?
  # Maybe we have similar issue of a plugin using old version of serverless_sdk https://github.com/serverless/serverless/issues/12101
  runtime: python3.11 # Latest is python3.11, As of now not supporting python3.12 . Should be the same Python version as we have in the GHA YML
  # Those two should be commented so we can support multi-Environments?
  # TODO Can we replace stage almost everywhere to ENVIRONMENT_NAME?
  # stage: "${env:STAGE}"
  # TODO Can we remove this and change it in environment:?
  # We must have this so we will have the environment name in the endpoint URL/URL and not /dev. provider: must have stage: so the endpoint URL will include the environment. It is not enought to have STAGE in the environment:
  stage: "${env:ENVIRONMENT_NAME}"
  # region: us-east-1

  # TODO All environment variables should be sync with GitHub Actions (GHA) Workflow test job environment variables 
  environment:
    # STAGE: "${env:STAGE}"
    STAGE: "${env:ENVIRONMENT_NAME}"

    BRAND_NAME: "${env:BRAND_NAME}"
    ENVIRONMENT_NAME: "${env:ENVIRONMENT_NAME}"

    LOGZIO_TOKEN: "${env:LOGZIO_TOKEN}"

    #JWT_SECRET_KEY: "${env:JWT_SECRET_KEY}"

    RDS_HOSTNAME: "${env:RDS_HOSTNAME}"
    RDS_USERNAME: "${env:RDS_USERNAME}"
    RDS_PASSWORD: "${env:RDS_PASSWORD}"

    PRODUCT_USER_IDENTIFIER: "${env:PRODUCT_USER_IDENTIFIER}"
    PRODUCT_PASSWORD: "${env:PRODUCT_PASSWORD}"

package:
  patterns:
    - "!node_modules/**"
    - "!package-lock.json"
    - "!package.json"

functions:
  get_code:
    handler: src.handler.google_account_get_code_lambda_handler
    timeout: 30
    #    The following are a few example events you can configure
    #    NOTE: Please make sure to change your handler code to work with those events
    #    Check the event documentation for details
    events:
      # HTTP API (API Gateway v2) https://www.serverless.com/framework/docs/providers/aws/events/http-api
      - http:
          path: /api/v1/googleAccount/getCode
          method: get
          cors: true


# TODO Please make sure it is sync with package.json
plugins:
  - serverless-offline
  - serverless-python-requirements
  #- serverless-secrets-plugin
