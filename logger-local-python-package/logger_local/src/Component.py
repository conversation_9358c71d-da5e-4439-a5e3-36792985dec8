from python_sdk_remote.mini_logger import <PERSON><PERSON><PERSON><PERSON> as logger

from .Connector import get_connection

# TODO Use Sql2Code in GHA to create this file everytime we run the build - circles-zone/sql2code-local-python-packge/sql_to_code/src/sqltocode.py running from GHA .github/workflows using sql2code-command-line parameter calling github-workflows/.github/workflows)  # noqa
# TODO should be similar to table_defintion.py (and also table_columns.py, ...)
component_cache = {
    1: {'component_id': 1, 'component_name': 'Unknown', 'component_type': None, 'component_category': None,
        'testing_framework': None, 'api_type': None},
    101: {'component_id': 101, 'component_name': 'Logger TypeScript', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    102: {'component_id': 102, 'component_name': 'Logger Python', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    111: {'component_id': 111, 'component_name': 'database without ORM', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    112: {'component_id': 112, 'component_name': 'database without ORM', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    113: {'component_id': 113, 'component_name': 'location_local', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    114: {'component_id': 114, 'component_name': 'importer_local', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    115: {'component_id': 115, 'component_name': 'external_user_local', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    116: {'component_id': 116, 'component_name': 'entity_type_local', 'component_type': None,
          'component_category': 'code',
          'testing_framework': None, 'api_type': None},
    117: {'component_id': 117, 'component_name': 'source data', 'component_type': None, 'component_category': 'code',
          'testing_framework': None, 'api_type': None},
    121: {'component_id': 121, 'component_name': 'database with ORM', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    122: {'component_id': 122, 'component_name': 'database with ORM', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    123: {'component_id': 123, 'component_name': 'contact', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    131: {'component_id': 131, 'component_name': 'url-typescript-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    132: {'component_id': 132, 'component_name': 'url-python-package', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    141: {'component_id': 141, 'component_name': 'storage', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    142: {'component_id': 142, 'component_name': 'storage', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    143: {'component_id': 143, 'component_name': 'text_block', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    144: {'component_id': 144, 'component_name': 'logger-remote-typescript-package', 'component_type': 'Remote',
          'component_category': None, 'testing_framework': None, 'api_type': None},
    145: {'component_id': 145, 'component_name': 'Education (Lesson)', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    146: {'component_id': 146, 'component_name': 'Real-Estate Realtor.com Selenium Import Service/Local Python Package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    147: {'component_id': 147, 'component_name': 'LOGGER_LOCAL_TYPESCRIPT_UNIT_TEST', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    148: {'component_id': 148, 'component_name': 'Logger', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    149: {'component_id': 149, 'component_name': 'Database without ORM', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    150: {'component_id': 150, 'component_name': 'Storage', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    151: {'component_id': 151, 'component_name': 'URL', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    152: {'component_id': 152, 'component_name': 'Database with ORM', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    153: {'component_id': 153, 'component_name': 'authentication', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    154: {'component_id': 154, 'component_name': 'React-Native', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    155: {'component_id': 155, 'component_name': 'queue', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    156: {'component_id': 156, 'component_name': 'Yelp', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    157: {'component_id': 157, 'component_name': 'profile-local-python', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    158: {'component_id': 158, 'component_name': 'Instagram', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    159: {'component_id': 159, 'component_name': 'queue-worker', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    160: {'component_id': 160, 'component_name': 'user registration', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    161: {'component_id': 161, 'component_name': 'user registration', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    162: {'component_id': 162, 'component_name': 'operational hours local', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    163: {'component_id': 163, 'component_name': 'Login React.js Frontend', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    164: {'component_id': 164, 'component_name': 'real-estate-realtor.com-selenium-imp-local-python-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    165: {'component_id': 165, 'component_name': 'BERT Machine Learning package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    166: {'component_id': 166, 'component_name': 'dialog workflow', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    167: {'component_id': 167, 'component_name': 'location-profile-local-python-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    168: {'component_id': 168, 'component_name': 'reaction-local-python-package.', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    169: {'component_id': 169,
          'component_name': 'https://github.com/circles-zone/profile-reaction-local-python-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    170: {'component_id': 170, 'component_name': 'profile-local-python-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    171: {'component_id': 171, 'component_name': 'user-local-python-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    172: {'component_id': 172, 'component_name': 'google contact sync', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    173: {'component_id': 173, 'component_name': 'whatsapp message local', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    174: {'component_id': 174, 'component_name': 'email local', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    175: {'component_id': 175, 'component_name': 'variable local', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    176: {'component_id': 176, 'component_name': 'storage remote', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    177: {'component_id': 177, 'component_name': 'number generator', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    178: {'component_id': 178, 'component_name': 'zoom importer local', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    179: {'component_id': 179, 'component_name': 'external user rest api', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': 'REST-API'},
    180: {'component_id': 180, 'component_name': 'location profile Rest Api', 'component_type': None,
          'component_category': 'REST-API', 'testing_framework': None, 'api_type': None},
    181: {'component_id': 181, 'component_name': 'TimeLine web socket', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    182: {'component_id': 182, 'component_name': 'SocialNetworks', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    183: {'component_id': 183, 'component_name': 'Menu Circles-User-Reactjs-Frontend', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    184: {'component_id': 184, 'component_name': 'sdk local', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    185: {'component_id': 185, 'component_name': 'Queue local', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    186: {'component_id': 186, 'component_name': 'external-user-remote-python-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    187: {'component_id': 187, 'component_name': 'Authentication Rest Api', 'component_type': 'REST-API',
          'component_category': None, 'testing_framework': None, 'api_type': None},
    188: {'component_id': 188, 'component_name': 'GOOGLE_CONTACT_LOCAL_PYTHON_PACKAGE', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    189: {'component_id': 189, 'component_name': 'user-context-remote-typescript', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    190: {'component_id': 190, 'component_name': 'Reddit', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    191: {'component_id': 191, 'component_name': 'authentication-remote-typescript-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    192: {'component_id': 192, 'component_name': 'user-registration-remote-restapi-typescript-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    193: {'component_id': 193, 'component_name': 'comunity waiting list', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    194: {'component_id': 194, 'component_name': 'gender detection remote', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    196: {'component_id': 196, 'component_name': 'timeline web socket', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    197: {'component_id': 197, 'component_name': 'user context python', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    198: {'component_id': 198, 'component_name': 'contact-person-profile-csv-imp-local', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    199: {'component_id': 199, 'component_name': 'profile user local python', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    200: {'component_id': 200, 'component_name': 'telephone number local python', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    201: {'component_id': 201, 'component_name': 'gender local python', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    202: {'component_id': 202, 'component_name': 'Debug local python', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    203: {'component_id': 203, 'component_name': 'gender detection data-extractor', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    204: {'component_id': 204, 'component_name': 'gender detection image-manipulation', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    205: {'component_id': 205, 'component_name': 'user registration', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    206: {'component_id': 206, 'component_name': 'Generic CRUD', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    207: {'component_id': 207, 'component_name': 'storage-local-python-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    208: {'component_id': 208, 'component_name': 'send sms using inforu python', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    209: {'component_id': 209, 'component_name': 'facebook message local python pacakge', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    210: {'component_id': 210, 'component_name': 'criteria local python pacakge', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    211: {'component_id': 211, 'component_name': 'group profile remote python pacakge', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    212: {'component_id': 212, 'component_name': 'api-management-local-python-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    213: {'component_id': 213, 'component_name': 'group-remote-python-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    214: {'component_id': 214, 'component_name': 'unified-json-api-python-pacakge', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    215: {'component_id': 215,
          'component_name': 'recruitment-employer-monster-com-indeed-com-local-scraper-restapi-python-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    216: {'component_id': 216, 'component_name': 'group-remote-typescript-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    217: {'component_id': 217, 'component_name': 'prompt-local-python-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    218: {'component_id': 218, 'component_name': 'profile-linkedin-scraper-imp-local-python-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    219: {'component_id': 219, 'component_name': 'profile-linkedin-scraper-imp-local-python-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    220: {'component_id': 220, 'component_name': 'location-profile-remote-typescript-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    221: {'component_id': 221, 'component_name': 'sql2code-local-python-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    222: {'component_id': 222, 'component_name': 'user-local-typescript-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    223: {'component_id': 223, 'component_name': 'contact-remote-typescript-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    224: {'component_id': 224, 'component_name': 'phone local typescript package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    225: {'component_id': 225, 'component_name': 'dialog-workflow-restapi-python-serverless-com',
          'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    227: {'component_id': 227, 'component_name': 'CAMPAIGN_TYPE_ORM_LOCAL_TYPESCRIPT', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    228: {'component_id': 228, 'component_name': 'OCCURENCE_TYPEORM_LOCAL_TYPESCRIPT', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    229: {'component_id': 229, 'component_name': 'group-local-restapi-typescript-serverless-com',
          'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    230: {'component_id': 230, 'component_name': 'profile-typeorm-local-typescript-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    231: {'component_id': 231, 'component_name': 'criteria-typeorm-local-typescript-package/pull/1',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    232: {'component_id': 232, 'component_name': 'contact-typeorm-local-typescript-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    233: {'component_id': 233, 'component_name': 'profile-metrics-local', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    234: {'component_id': 234, 'component_name': 'profile-url-local-python-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    235: {'component_id': 235, 'component_name': 'market_place_good_typeorm_local_typescript', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    236: {'component_id': 236, 'component_name': 'machine learning local python', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    237: {'component_id': 237, 'component_name': 'relationship-type-typeorm-local-typescript-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    238: {'component_id': 238, 'component_name': 'generic_crud_ml', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    239: {'component_id': 239, 'component_name': 'marketplace good grpahql typescript package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    240: {'component_id': 240, 'component_name': 'facebook api graphql importer', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    241: {'component_id': 241, 'component_name': 'relationship-type-graphql-server-typescript-serverless-com',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    242: {'component_id': 242, 'component_name': 'criteria-promotional-message-to-person-local-python-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    243: {'component_id': 243, 'component_name': 'message-send-platofrom-invitation-local-python-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    244: {'component_id': 244, 'component_name': 'star-local-python-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    245: {'component_id': 245, 'component_name': 'profile-facebook-selenium-scraper-imp-local-python-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    246: {'component_id': 246, 'component_name': 'event-ticketmaster-graphql-imp-local-python-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    247: {'component_id': 247, 'component_name': 'event-local-restapi-python-serverless-com holidays',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    248: {'component_id': 248, 'component_name': 'event-remote-restapi-python-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    249: {'component_id': 249, 'component_name': 'Dialog Workflow React.js', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    250: {'component_id': 250,
          'component_name': 'https://github.com/circles-zone/twitter-message-twitter-local-python-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    251: {'component_id': 251, 'component_name': 'event-external-local-python-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    252: {'component_id': 252, 'component_name': 'dialog workflow remote', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    253: {'component_id': 253, 'component_name': 'reactjs-sdk-typescript-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    254: {'component_id': 254, 'component_name': 'label-message-local-python-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    255: {'component_id': 255, 'component_name': 'profile display', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    256: {'component_id': 256, 'component_name': 'queue-restapi-python-serverless-com', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    257: {'component_id': 257, 'component_name': 'https://github.com/circles-zone/queue-remote-typescript-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    258: {'component_id': 258, 'component_name': 'SmartLink Generator Python Remote', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    259: {'component_id': 259, 'component_name': 'messages local', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    260: {'component_id': 260, 'component_name': 'event restapi remote', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    261: {'component_id': 261, 'component_name': 'multi language  drop down list', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    262: {'component_id': 262, 'component_name': 'https://github.com/circles-zone/circles-user-reactjs-frontend',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    263: {'component_id': 263,
          'component_name': 'https://github.com/circles-zone/message-typeorm-local-typescript-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    264: {'component_id': 264, 'component_name': 'Platform Remote', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    265: {'component_id': 265, 'component_name': 'people-local Python', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    266: {'component_id': 266, 'component_name': 'message-graphql-remote-typescript-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    267: {'component_id': 267, 'component_name': 'queue-remote', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    268: {'component_id': 268, 'component_name': 'App.tsx', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    269: {'component_id': 269, 'component_name': 'https://github.com/circles-zone/contact-group-local-python-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    270: {'component_id': 270,
          'component_name': 'https://github.com/circles-zone/group-profile-remote-restapi-typescript-package',
          'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    271: {'component_id': 271, 'component_name': 'Platform ReactJS frontend', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    272: {'component_id': 272, 'component_name': 'QUEUE_REMOTE_TYPESCRIPT', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    273: {'component_id': 273, 'component_name': 'EVENT_PROFILE_TYPESCRIPT', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    274: {'component_id': 274, 'component_name': 'contact-person-local', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    275: {'component_id': 275, 'component_name': 'contact-email-addresses', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    276: {'component_id': 276, 'component_name': 'contact-notes-local', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    277: {'component_id': 277, 'component_name': 'Invitation local restapi typescript package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    278: {'component_id': 278, 'component_name': 'contact-user-externals-local', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    279: {'component_id': 279, 'component_name': 'contact-profiles-local', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    280: {'component_id': 280, 'component_name': 'api-management-typescript-package', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    281: {'component_id': 281, 'component_name': 'contact-locations-local', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    282: {'component_id': 282, 'component_name': 'Campaign Python', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    283: {'component_id': 283, 'component_name': 'action-items-local', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    284: {'component_id': 284, 'component_name': 'labels-local', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    285: {'component_id': 285, 'component_name': 'groups-local', 'component_type': None, 'component_category': None,
          'testing_framework': None, 'api_type': None},
    286: {'component_id': 286, 'component_name': 'organizations-local', 'component_type': None,
          'component_category': None,
          'testing_framework': None, 'api_type': None},
    287: {'component_id': 287, 'component_name': 'organization-profile-local', 'component_type': None,
          'component_category': None, 'testing_framework': None, 'api_type': None},
    1001: {'component_id': 1001, 'component_name': 'people', 'component_type': None, 'component_category': None,
           'testing_framework': None, 'api_type': None},
    2001: {'component_id': 2001, 'component_name': 'contact', 'component_type': None, 'component_category': None,
           'testing_framework': None, 'api_type': None},
    3001: {'component_id': 3001, 'component_name': 'user', 'component_type': None, 'component_category': None,
           'testing_framework': None, 'api_type': None},
    4001: {'component_id': 4001, 'component_name': 'profile', 'component_type': None, 'component_category': None,
           'testing_framework': None, 'api_type': None},
    5000: {'component_id': 5000, 'component_name': 'user registration', 'component_type': None,
           'component_category': None,
           'testing_framework': None, 'api_type': None},
    6000: {'component_id': 6000, 'component_name': 'group', 'component_type': None, 'component_category': None,
           'testing_framework': None, 'api_type': None},
    7001: {'component_id': 7001, 'component_name': 'DATABASE_MYSQL_GENERIC_CRUD_ML_COMPONENT_ID',
           'component_type': None,
           'component_category': None, 'testing_framework': None, 'api_type': None},
    7002: {'component_id': 7002, 'component_name': 'DATABASE_MYSQL_PYTHON_GENERIC_MAPPING_COMPONENT_ID',
           'component_type': None, 'component_category': None, 'testing_framework': None, 'api_type': None},
    8001: {'component_id': 8001, 'component_name': 'location-remote-graphql-typescript-package', 'component_type': None,
           'component_category': None, 'testing_framework': None, 'api_type': None},
    10001: {'component_id': 10001, 'component_name': 'group-profile', 'component_type': None,
            'component_category': None,
            'testing_framework': None, 'api_type': None},
    5000001: {'component_id': 5000001, 'component_name': None, 'component_type': None, 'component_category': None,
              'testing_framework': None, 'api_type': None},
    5000002: {'component_id': 5000002, 'component_name': 'logger check1', 'component_type': None,
              'component_category': None, 'testing_framework': None, 'api_type': None},
    5000003: {'component_id': 5000003, 'component_name': 'logger check2', 'component_type': None,
              'component_category': None, 'testing_framework': None, 'api_type': None},
    5000004: {'component_id': 5000004, 'component_name': 'domain local', 'component_type': None,
              'component_category': None, 'testing_framework': None, 'api_type': None}}


class Component:
    @staticmethod
    def get_details_by_component_id(component_id: int) -> dict:
        component_id = int(component_id)
        if component_id in component_cache:
            return component_cache[component_id]
        logger.start("Logger.Component.get_details_by_component_id",
                     object={"component_id": component_id})
        if logger.is_write_to_sql():
            try:
                connection = get_connection(schema_name="component")
                cursor = connection.cursor()
                sql_query = ("SELECT name, component_type, component_category, testing_framework, api_type "
                             "FROM component.component_view WHERE component_id = %s")
                cursor.execute(sql_query, (component_id,))
                result = cursor.fetchone()
                if not result:
                # TODO: don't stop if in production, but in any case insert the error to the logger table
                    raise Exception(f"Component {component_id} not found in the component.component_table")
                component_json = {
                  "component_id": component_id,
                  "component_name": result[0],
                  "component_type": result[1],
                  "component_category": result[2],
                  "testing_framework": result[3],
                  "api_type": result[4]
                }
                component_cache[component_id] = component_json
                return component_json
            except Exception as exception:
                logger.exception(
                  f"Logger.Component.get_details_by_component_id component_id={component_id}", exception)
                component_cache[component_id] = {}
                # TODO Can we do except: for this exception
                if " denied " not in str(exception).lower():
                   print(f"logger-local Component.py get_details_by_component_id({component_id}) got exception: {exception}")
                   raise exception
                return {}
            finally:
                logger.end("Logger.Component.get_details_by_component_id",
                           object=component_cache[component_id])
        else:
              print(f"logger-local Component.py get_details_by_component_id({component_id} didn't find in cache and can't access the database due to LOGGER_IS_WRITE_TO_SQL=False TODO Please make sure the component exist in the logger-local cache) ")
        logger.end("Logger.Component.get_details_by_component_id() couldn't find the details as not in cache and no access to the database",
                   object={"component_id": component_id})