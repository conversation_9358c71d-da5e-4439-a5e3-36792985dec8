import logging
import re
import unittest

from src.MetaLogger import Lo<PERSON>, module_wrapper
from .constants import logger_test_object

# TODO: change to True, and fix the end test to return the correct function name instead of wrapper
ASSERT_LOGGER_END = False


def foo():
    1 / 0  # noqa


def bar():
    return 1


logger = Logger.create_logger(object=logger_test_object, level="debug", ignore_cached_logger=True)
module_wrapper(logger)


class TestModuleWrapper(unittest.TestCase):
    def test_foo(self):
        with self.assertLogs(logger="src.LoggerLocal", level="ERROR") as log_context:
            try:
                foo()
            except ZeroDivisionError:
                pass  # we expect this exception
        self.assertEqual(len(log_context.output), 1)

    def test_bar(self):
        with self.assertLogs(logger="src.LoggerLocal", level=logging.DEBUG) as log_context:
            bar()

        start_regex = r".*START.*bar \| kwargs={}"
        assert any(re.match(start_regex, line) for line in
                   log_context.output), f"regex: {start_regex} not found in logs {log_context.output}"

        if ASSERT_LOGGER_END:
            end_regex = r".*END.*bar \| kwargs={'1': 1}"
            assert any(re.match(end_regex, line) for line in log_context.output), f"regex: {end_regex} not found in logs {log_context.output}"
