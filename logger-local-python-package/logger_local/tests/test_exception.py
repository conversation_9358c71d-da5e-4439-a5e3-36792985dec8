import re
import time

from src.Connector import get_connection
from src.LoggerLocal import Logger
from src.MessageSeverity import MessageSeverity
from .constants import logger_test_object

logger = Logger.create_logger(object=logger_test_object, level="Error",
                              ignore_cached_logger=True)


def test_two_exceptions():
    # TODO Shall we respect the LOGGER_IS_WRITE_TO_SQL environment variable?
    # logger._is_write_to_sql = True
    try:
        try:
            1 / 0
        except Exception as e:
            logger.exception("Exception1", object=e)
        finally:
            logger.info("End test")
    except Exception as e:
        logger.error("Exception2", object=e)

    _assert_tests(is_one_exception=False)


def test_one_exception():
    # TODO Shall we respect the LOGGER_IS_WRITE_TO_SQL environment variable?
    # logger._is_write_to_sql = True
    try:
        try:
            1 / 0
        except Exception as e:
            logger.error("Exception1", object=e)
            raise e
        finally:
            logger.info("End test")
    except Exception as e:
        logger.error("Exception2", object=e)

    _assert_tests(is_one_exception=True)


def _assert_tests(is_one_exception: bool):
    time.sleep(5)  # wait for async write to sql
    # TODO Shall we respect the LOGGER_IS_WRITE_TO_SQL environment variable?
    # TODO Why? Please docstring
    logger._is_write_to_sql = False
    query = "SELECT * FROM logger.logger_view WHERE severity_id >= %s ORDER BY logger_id DESC LIMIT 2"
    connection = get_connection("logger")
    connection.commit()
    cursor = connection.cursor(dictionary=True)
    cursor.execute(query, (MessageSeverity.ERROR.value,))
    result = cursor.fetchall()
    assert result is not None, "_asset_tests() No matching record found"
    # TODO Replace all Magic Numbers with constants
    assert len(result) == 2
    function_name = 'test_two_exceptions' if not is_one_exception else 'test_one_exception'
    assert result[1]['function_name'] == function_name
    assert result[0]['function_name'] == function_name

    if is_one_exception:
        assert result[1]['severity_id'] == MessageSeverity.ERROR.value
    else:
        assert result[1]['severity_id'] == MessageSeverity.EXCEPTION.value
    assert result[0]['severity_id'] == MessageSeverity.ERROR.value

    assert result[1]['message'] == 'Exception1'
    assert result[0]['message'] == 'Exception2'

    assert result[1]['error_stack'].startswith(r"['Traceback (most recent call last):\n'")
    assert result[0]['error_stack'].startswith(r"['Traceback (most recent call last):\n'")

    assert result[1]['error_stack'].endswith(r"'ZeroDivisionError: division by zero\n']")
    assert result[0]['error_stack'].endswith(r"'ZeroDivisionError: division by zero\n']")

    assert len(set(re.findall(r'line \d+', result[1]['error_stack']))) == 1
    assert len(set(re.findall(r'line \d+', result[0]['error_stack']))) == 3 - int(is_one_exception)
