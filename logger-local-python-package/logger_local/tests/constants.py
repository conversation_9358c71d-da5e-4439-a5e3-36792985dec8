from src.LoggerComponentEnum import LoggerComponentEnum

LOGGER_COMPONENT_ID = 102
LOGGER_COMPONENT_NAME = "Logger Python"
LOGGER_COMPONENT_CATEGORY = LoggerComponentEnum.ComponentCategory.Unit_Test.value
LOGGER_DEVELOPER_EMAIL = '<EMAIL>'

logger_test_object = {
    'component_id': LOGGER_COMPONENT_ID,
    'component_name': LOGGER_COMPONENT_NAME,
    'component_category': LOGGER_COMPONENT_CATEGORY,
    'developer_email': LO<PERSON><PERSON><PERSON>_DEVELOPER_EMAIL
}
